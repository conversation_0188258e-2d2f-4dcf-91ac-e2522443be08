/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.fulfillmen.starter.data.mp.tenant.DefaultTenantContext;
import com.fulfillmen.starter.data.mp.tenant.TenantContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 多租户配置
 *
 * <AUTHOR>
 * @date 2025/6/17 17:55
 * @description: 多租户配置，包括租户上下文、租户处理器、租户拦截器等
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class TenantConfig {

    /**
     * 需要忽略多租户处理的表名
     */
    private static final Set<String> TENANT_IGNORE_TABLES = new HashSet<>(Arrays.asList(
        // 租户相关表本身不需要租户隔离
        "tenants", "tenant_plans", "tenant_plan_relation", "tenants_info", "tenant_domains", "tenant_locales", "tenant_commission_config", "tenant_files",
        // 商品库映射信息
      "pdc_product_mapping", "sys_alibaba_category", "sys_alibaba_callback_logs",
        // openapi
        "openapi_account", "openapi_account_permission", "openapi_interface", "regions", "subregions",
        // 系统配置表
        "sys_config", "sys_users", "sys_option"));

    /**
     * 租户上下文Bean - 提供获取当前租户ID的能力
     */
    @Bean
    @ConditionalOnMissingBean(TenantContext.class)
    public TenantContext tenantContext() {
        return new ShopTenantContext();
    }

    /**
     * 租户处理器Bean - 处理SQL中的租户ID注入
     */
    @Bean
    @ConditionalOnMissingBean(TenantLineHandler.class)
    public TenantLineHandler tenantLineHandler(TenantContext tenantContext) {
        return new ShopTenantLineHandler(tenantContext);
    }

    /**
     * 租户拦截器Bean - MyBatis Plus插件
     */
    @Bean
    public TenantLineInnerInterceptor tenantLineInnerInterceptor(TenantLineHandler tenantLineHandler) {
        TenantLineInnerInterceptor interceptor = new TenantLineInnerInterceptor();
        interceptor.setTenantLineHandler(tenantLineHandler);
        log.info("多租户拦截器已配置，忽略表: {}", TENANT_IGNORE_TABLES);
        return interceptor;
    }

    /**
     * 自定义租户上下文实现 由于租户解析逻辑已统一到 TenantResolverService，这里只使用ThreadLocal
     */
    public static class ShopTenantContext extends DefaultTenantContext {
        // 继承默认实现即可，租户ID通过TenantInterceptor统一设置到ThreadLocal

    }

    /**
     * 自定义租户处理器实现 处理租户ID的注入和表过滤
     */
    public static class ShopTenantLineHandler implements TenantLineHandler {

        private final TenantContext tenantContext;

        public ShopTenantLineHandler(TenantContext tenantContext) {
            this.tenantContext = tenantContext;
        }

        @Override
        public Expression getTenantId() {
            String tenantId = tenantContext.getCurrentTenantId();
            if (tenantId == null) {
                return null;
            }

            // 如果是数字格式的租户ID，直接返回
            try {
                Long.parseLong(tenantId);
                return new StringValue(tenantId);
            } catch (NumberFormatException e) {
                // 如果是字符串格式（如子域名），可能需要转换为对应的租户ID
                // 这里可以添加查询逻辑将子域名转换为租户ID
                log.warn("租户标识不是数字格式: {}", tenantId);
                return null;
            }
        }

        @Override
        public String getTenantIdColumn() {
            return "tenant_id";
        }

        @Override
        public boolean ignoreTable(String tableName) {
            // 忽略租户相关表和系统表
            return TENANT_IGNORE_TABLES.contains(tableName.toLowerCase());
        }
    }
}
