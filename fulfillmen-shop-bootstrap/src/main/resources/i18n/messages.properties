# ==========================================
# Fulfillmen Global I18n Messages (English)
# ==========================================

# ==================== 通用参数错误 1000-1099 ====================
global.param.error=Request parameter error: {0}
global.param.missing=Required parameter is missing: {0}
global.param.format.error=Parameter format error: {0}
global.param.validation.error=Parameter validation failed: {0}
global.method.not.supported=Request method {0} not supported
global.media.type.not.supported=Media type {0} not supported
global.json.format.error=JSON format error: {0}
global.data.format.error=Data format error: {0}
global.file.size.exceeded=File size exceeded limit {0}MB
global.file.type.not.supported=File type {0} not supported
global.param.cannot.be.empty=Parameter {0} cannot be empty
global.verification.code.expired=Verification code expired

# ==================== 认证授权错误 1100-1199 ====================
global.auth.not.login=Not logged in, please login first
global.auth.login.failed=Login failed: {0}
global.auth.user.not.found=User {0} not found
global.auth.password.error=Password error
global.auth.permission.denied=Permission denied to access {0}
global.auth.token.invalid=Token invalid or malformed
global.auth.token.expired=Token expired, please login again
global.auth.account.disabled=Account {0} is disabled
global.auth.account.locked=Account {0} is locked
global.auth.captcha.error=Captcha error: {0}
global.auth.kick.out=You have been kicked offline
global.auth.be.replaced.message=You have been replaced

# ==================== 系统错误 1200-1299 ====================
global.system.error=System internal error: {0}
global.database.connection.error=Database connection error: {0}
global.database.operation.error=Database operation failed: {0}
global.cache.operation.error=Cache operation failed: {0}
global.config.error=Configuration error: {0}
global.resource.not.found=Resource {0} not found
global.resource.already.exists=Resource {0} already exists
global.operation.too.frequent=Operation too frequent, please try again later
global.service.unavailable=Service {0} temporarily unavailable
global.concurrent.conflict=Concurrent operation conflict
global.operation.failed=Operation failed
global.unable.retrieve.wms.account=Unable to retrieve WMS account information
global.no.relevant.information.found=No relevant information found

# ==================== 外部服务错误 1300-1399 ====================
global.network.error=Network connection error: {0}
global.network.timeout=Network timeout after {0} seconds
global.third.party.service.error=Third party service {0} error: {1}
global.third.party.service.unavailable=Third party service {0} unavailable
global.api.call.failed=API call to {0} failed: {1}
global.response.parse.error=Response parse error: {0}
global.remote.service.error=Remote service returned error: {0}
global.rate.limit.error=Request too frequent, please try again after {1} seconds (limit: {0} requests)
global.rate.limit.exceeded=Rate limit exceeded: {0}/{1} requests in window, retry after {2} seconds
global.rate.limit.user.exceeded=User rate limit exceeded: {0} requests per {1}, please try again later
global.rate.limit.ip.exceeded=IP rate limit exceeded: {0} requests per {1}, please try again later
global.rate.limit.api.exceeded=API rate limit exceeded: {0} requests per {1}, please try again later
global.circuit.breaker.open=Circuit breaker for {0} is open
global.service.degraded=Service {0} degraded to fallback mode

# ==================== 签名验证错误 1310-1329 ====================
global.signature.param.missing=Missing signature parameter: {0}
global.signature.invalid=Invalid signature
global.signature.validation.error=Signature validation error: {0}
global.signature.required.params.missing=Missing required signature parameters: {0}
global.timestamp.expired=Request timestamp is too old or too future
global.timestamp.format.error=Invalid timestamp format: {0}

# ==================== 加密解密错误 1330-1339 ====================
global.field.encryption.error=Field encryption error: {0}
global.field.decrypt.error=Field decrypt error: {0}

# ==================== 分类管理错误 1340-1349 ====================
global.category.get.failed=Failed to get category: {0}
global.category.translation.get.failed=Failed to get category translation: {0}
global.category.attributes.get.failed=Failed to get category attributes: {0}

# ==================== 产品管理错误 1350-1369 ====================
global.product.detail.get.failed=Failed to get product detail: {0}
global.product.search.failed=Failed to search products: {0}
global.invalid.file.type=Invalid file type: {0}
global.file.process.failed=Failed to process file: {0}
global.file.upload.failed=Failed to upload file: {0}
global.image.search.failed=Failed to search by image: {0}
global.seller.products.get.failed=Failed to get seller products: {0}
global.keyword.navigation.get.failed=Failed to get keyword navigation: {0}
global.keyword.cannot.be.empty=Keyword cannot be empty
global.image.id.cannot.be.empty=Image ID cannot be empty
global.image.upload.failed=Failed to upload image: {0}
global.image.process.failed=Failed to process image: {0}
global.recommend.products.get.failed=Failed to get recommend products: {0}
global.related.recommend.products.get.failed=Failed to get related recommend products: {0}

# ==================== 产品详情相关错误 1390-1399 ====================
validation.product.not.found=Product not found: {0}
validation.product.detail.cache.expired=Product detail cache expired for: {0}
validation.product.data.incomplete=Product data incomplete: {0}
validation.product.sync.timeout=Product synchronization timeout: {0}
validation.product.detail.service.degraded=Product detail service degraded, using fallback: {0}

# ==================== 数据库操作错误 1370-1379 ====================
global.batch.insert.failed=Batch insert operation failed: {0}
global.batch.update.failed=Batch update operation failed: {0}
global.alibaba.categories.get.failed=Failed to get alibaba categories: {0}

# ==================== 异常处理相关错误 1380-1399 ====================
global.validation.failed=Parameter validation failed
global.request.validation.failed=Request parameter validation failed: {0}
global.constraint.validation.failed=Constraint validation failed
global.request.constraint.validation.failed=Request constraint validation failed: {0}
global.data.format.validation.error=Data format error, please check input content length
global.data.duplicate.error=Data duplication, please check input content
global.data.constraint.validation.error=Data constraint error, please check input data
global.data.processing.failed=Data processing failed, please try again later
global.database.operation.failed=Database operation failed, please try again later
global.data.access.failed=Data access failed, please try again later
global.parameter.type.mismatch=Parameter type mismatch
global.request.parameter.error=Request parameter error: {0}
global.file.upload.error=File upload failed
global.single.file.size.exceeded=Single file size exceeds limit
global.total.file.size.exceeded=Total file size exceeds limit
global.file.size.with.value.exceeded=File size {0}MB exceeds limit
global.file.size.conversion.failed=File size conversion failed: {0}
global.internal.system.error=Internal system error, please contact administrator

# ==================== 订单相关错误 ====================
global.order.preview.token.expired=Order preview token expired
global.order.preview.token.not.found=Order preview token not found
global.order.preview.token.data.error=Order preview token data error
global.order.pay.failed=Payment failed, insufficient balance
global.order.pay.failed.unknown=Payment failed, unknown error
global.order.submit.failed=Order submit failed, please try again later
global.freight.estimate.failed=Freight estimate failed
# ==================== 租户相关错误 ====================
global.tenant.warehouse.not.found=Tenant warehouse not found
