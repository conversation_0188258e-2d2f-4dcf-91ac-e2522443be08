# ==========================================
# Fulfillmen 全局国际化消息（中文）
# ==========================================
# ==================== 通用参数错误 1000-1099 ====================
global.param.error=请求参数错误：{0}
global.param.missing=缺少必需参数：{0}
global.param.format.error=参数格式错误：{0}
global.param.validation.error=参数校验失败：{0}
global.method.not.supported=不支持的请求方法：{0}
global.media.type.not.supported=不支持的媒体类型：{0}
global.json.format.error=JSON格式错误：{0}
global.data.format.error=数据格式错误：{0}
global.file.size.exceeded=文件大小超过限制：{0}MB
global.file.type.not.supported=不支持的文件类型：{0}
global.param.cannot.be.empty=参数 {0} 不能为空
global.unsupported.search.type=不支持的搜索类型：{0}
global.verification.code.expired=验证码已过期
# ==================== 认证授权错误 1100-1199 ====================
global.auth.not.login=未登录，请先登录
global.auth.login.failed=登录失败：{0}
global.auth.user.not.found=用户 {0} 不存在
global.auth.password.error=密码错误
global.auth.permission.denied=无权限访问：{0}
global.auth.token.invalid=令牌无效或格式错误
global.auth.token.expired=令牌已过期，请重新登录
global.auth.account.disabled=账户 {0} 已被禁用
global.auth.account.locked=账户 {0} 已被锁定
global.auth.captcha.error=验证码错误：{0}
global.auth.kick.out=您已被踢下线
global.auth.be.replaced.message=您已被顶下线
# ==================== 系统错误 1200-1299 ====================
global.system.error=系统内部错误：{0}
global.database.connection.error=数据库连接错误：{0}
global.database.operation.error=数据库操作失败：{0}
global.cache.operation.error=缓存操作失败：{0}
global.config.error=配置错误：{0}
global.resource.not.found=资源 {0} 不存在
global.resource.already.exists=资源 {0} 已存在
global.operation.too.frequent=操作过于频繁，请稍后再试
global.service.unavailable=服务 {0} 暂时不可用
global.concurrent.conflict=并发操作冲突
global.operation.failed=操作失败
global.unable.retrieve.wms.account=无法检索 WMS 帐户信息
global.no.relevant.information.found=未找到相关的信息
# ==================== 外部服务错误 1300-1399 ====================
global.network.error=网络连接错误：{0}
global.network.timeout=网络超时，等待 {0} 秒
global.third.party.service.error=第三方服务 {0} 错误：{1}
global.third.party.service.unavailable=第三方服务 {0} 不可用
global.api.call.failed=调用接口 {0} 失败：{1}
global.response.parse.error=响应解析错误：{0}
global.remote.service.error=远程服务返回错误：{0}
global.rate.limit.error=请求过于频繁，请在 {1} 秒后重试（限制：{0} 次请求）
global.rate.limit.exceeded=限流触发：时间窗口内已处理 {0}/{1} 个请求，请在 {2} 秒后重试
global.rate.limit.user.exceeded=用户限流触发：{1} 内最多 {0} 次请求，请稍后再试
global.rate.limit.ip.exceeded=IP限流触发：{1} 内最多 {0} 次请求，请稍后再试
global.rate.limit.api.exceeded=接口限流触发：{1} 内最多 {0} 次请求，请稍后再试
global.circuit.breaker.open=服务 {0} 的熔断器已开启
global.service.degraded=服务 {0} 已降级到备用模式
# ==================== 签名验证错误 1310-1329 ====================
global.signature.param.missing=缺少签名参数：{0}
global.signature.invalid=无效的签名
global.signature.validation.error=签名验证错误：{0}
global.signature.required.params.missing=缺少必需的签名参数：{0}
global.timestamp.expired=请求时间戳过旧或未来时间
global.timestamp.format.error=无效的时间戳格式：{0}
# ==================== 加密解密错误 1330-1339 ====================
global.field.encryption.error=字段加密错误：{0}
global.field.decrypt.error=字段解密错误：{0}
# ==================== 分类管理错误 1340-1349 ====================
global.category.get.failed=获取分类失败：{0}
global.category.translation.get.failed=获取分类翻译失败：{0}
global.category.attributes.get.failed=获取分类属性失败：{0}
# ==================== 产品管理错误 1350-1369 ====================
global.product.detail.get.failed=获取商品详情失败：{0}
global.product.search.failed=商品搜索失败：{0}
global.invalid.file.type=无效的文件类型：{0}
global.file.process.failed=文件处理失败：{0}
global.file.upload.failed=文件上传失败：{0}
global.image.search.failed=图片搜索失败：{0}
global.seller.products.get.failed=获取卖家商品失败：{0}
global.keyword.navigation.get.failed=获取关键词导航失败：{0}
global.keyword.cannot.be.empty=关键词不能为空
global.image.id.cannot.be.empty=图片ID不能为空
global.image.upload.failed=图片上传失败：{0}
global.image.process.failed=图片处理失败：{0}
global.recommend.products.get.failed=获取推荐商品失败：{0}
global.related.recommend.products.get.failed=获取相关推荐商品失败：{0}
# ==================== 产品详情相关错误 1390-1399 ====================
validation.product.not.found=商品不存在：{0}
validation.product.detail.cache.expired=商品详情缓存已过期：{0}
validation.product.data.incomplete=商品数据不完整：{0}
validation.product.sync.timeout=商品同步超时：{0}
validation.product.detail.service.degraded=商品详情服务降级，使用备用方案：{0}
# ==================== 数据库操作错误 1370-1379 ====================
global.batch.insert.failed=批量插入失败：{0}
global.batch.update.failed=批量更新失败：{0}
global.alibaba.categories.get.failed=获取阿里巴巴分类失败：{0}
# ==================== 异常处理相关错误 1380-1399 ====================
global.validation.failed=参数验证失败
global.request.validation.failed=请求参数验证失败：{0}
global.constraint.validation.failed=约束验证失败
global.request.constraint.validation.failed=请求约束验证失败：{0}
global.data.format.validation.error=数据格式错误，请检查输入内容长度
global.data.duplicate.error=数据重复，请检查输入内容
global.data.constraint.validation.error=数据约束错误，请检查输入数据
global.data.processing.failed=数据处理失败，请稍后重试
global.database.operation.failed=数据库操作失败，请稍后重试
global.data.access.failed=数据访问失败，请稍后重试
global.parameter.type.mismatch=参数类型不匹配
global.request.parameter.error=请求参数错误：{0}
global.file.upload.error=文件上传失败
global.single.file.size.exceeded=单个文件大小超出限制
global.total.file.size.exceeded=文件总大小超出限制
global.file.size.with.value.exceeded=文件大小 {0}MB 超出限制
global.file.size.conversion.failed=文件大小转换失败：{0}
global.internal.system.error=系统内部错误，请联系管理员
# ==================== 订单相关错误 ====================
global.order.preview.token.expired=订单预览令牌已过期
global.order.preview.token.not.found=订单预览令牌不存在
global.order.preview.token.data.error=订单预览令牌数据异常
global.order.pay.failed=支付失败，余额不足
global.order.pay.failed.unknown=支付失败，未知错误
global.order.submit.failed=订单提交失败, 请稍后重试
global.freight.estimate.failed=运费预估失败
# ==================== 租户相关错误 ====================
global.tenant.warehouse.not.found=租户默认仓库不存在
