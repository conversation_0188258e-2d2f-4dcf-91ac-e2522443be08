/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.event;

import com.fulfillmen.shop.manager.config.ProductSyncConfig;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 产品同步事件监听器
 *
 * <p>
 * 🎯 循环依赖解决方案的核心组件：
 *
 * 架构设计：
 * PdcProductMappingRepository → 发布事件 → ProductSyncEventListener → ProductSyncService
 *
 * 优势：
 * 1. 解耦：Repository 不直接依赖 Service，通过事件通信
 * 2. 异步：不阻塞主业务流程，提升响应速度
 * 3. 可配置：支持开关控制，便于调试和维护
 * 4. 可监控：事件处理过程完全可观测
 * 5. 可扩展：未来可以添加更多处理逻辑
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/18
 * @since 1.0.0
 */
@Slf4j
@Component
public class ProductSyncEventListener {

    private final IProductSyncService productSyncService;
    private final ProductSyncConfig productSyncConfig;

    /**
     * 构造函数注入
     *
     * 使用 @Qualifier 明确指定使用 ProductSyncServiceImpl 实现，
     * 避免与 ProductSyncServiceV2Impl 产生依赖注入冲突
     */
    public ProductSyncEventListener(@Qualifier("productSyncServiceImpl") IProductSyncService productSyncService,
        ProductSyncConfig productSyncConfig) {
        this.productSyncService = productSyncService;
        this.productSyncConfig = productSyncConfig;
    }
//
//    /**
//     * 监听产品数据同步事件
//     *
//     * <p>
//     * 使用 @Async 注解实现异步处理，避免阻塞主业务流程
//     * </p>
//     *
//     * @param event 产品数据同步事件
//     */
//    @Async
//    @EventListener
//    public void handleProductDataSyncEvent(ProductDataSyncEvent event) {
//        // 1. 检查功能开关
//        if (!productSyncConfig.isAutoSyncEnabled()) {
//            log.debug("自动同步功能已禁用，跳过事件处理: {}", event.getEventSummary());
//            return;
//        }
//
//        // 2. 验证事件数据
//        if (CollectionUtil.isEmpty(event.getPlatformProductIds())) {
//            log.debug("事件中产品ID列表为空，跳过处理: {}", event.getEventSummary());
//            return;
//        }
//
//        log.info("🔄 开始处理产品同步事件: {}", event.getEventSummary());
//
//        try {
//            // 3. 执行批量同步
//            long startTime = System.currentTimeMillis();
//
//            productSyncService.autoSyncFromPdcMapping(event.getPlatformProductIds());
//
//            long duration = System.currentTimeMillis() - startTime;
//
//            log.info("✅ 产品同步事件处理完成: {} - 耗时: {}ms", event.getEventSummary(), duration);
//
//            // 4. 性能监控
//            if (duration > productSyncConfig.getMonitoring().getSlowSyncThresholdMs()) {
//                log.warn("⚠️ 慢同步检测: {} - 耗时: {}ms (阈值: {}ms)", event.getEventSummary(), duration, productSyncConfig
//                    .getMonitoring()
//                    .getSlowSyncThresholdMs());
//            }
//
//        } catch (Exception e) {
//            log.error("❌ 产品同步事件处理失败: {} - 错误: {}", event.getEventSummary(), e.getMessage(), e);
//
//            // 可以在这里添加重试逻辑或告警通知
//            handleSyncFailure(event, e);
//        }
//    }

    /**
     * 处理同步失败情况
     *
     * @param event 失败的事件
     * @param e     异常信息
     */
    private void handleSyncFailure(ProductDataSyncEvent event, Exception e) {
        // 记录失败统计
        log.warn("同步失败事件详情 - 类型: {}, 产品数量: {}, 描述: {}, 异常: {}", event.getEventType(), event.getPlatformProductIds()
            .size(), event.getDescription(), e.getClass().getSimpleName());

        // TODO: 可以在这里实现以下功能：
        // 1. 失败重试机制
        // 2. 告警通知
        // 3. 失败统计收集
        // 4. 降级处理策略
    }
}
