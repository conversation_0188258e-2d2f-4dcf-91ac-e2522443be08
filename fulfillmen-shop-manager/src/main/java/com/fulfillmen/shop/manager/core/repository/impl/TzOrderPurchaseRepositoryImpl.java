/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.dao.mapper.TzShoppingCartMapper;
import com.fulfillmen.shop.domain.convert.order.UserPurchaseOrderDTOConvert;
import com.fulfillmen.shop.domain.dto.order.UserPurchaseOrderDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 订单仓储实现
 *
 * <pre>
 * 负责订单相关的数据持久化操作，集成状态管理系统：
 * 1. 创建完整的订单数据（采购订单、供应商订单、订单项）
 * 2. 初始化订单状态管理字段
 * 3. 触发状态计算和同步
 * 4. 处理购物车清理
 * 5. 确保数据一致性
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/30 14:50
 * @description 订单仓储层实现，集成状态管理系统
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TzOrderPurchaseRepositoryImpl extends CrudRepository<TzOrderPurchaseMapper, TzOrderPurchase> implements TzOrderPurchaseRepository {

    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TzOrderItemMapper orderItemMapper;
    private final TzShoppingCartMapper shoppingCartMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 创建采购订单 - 性能优化版本
     *
     * <pre>
     * 优化后的订单创建流程：
     * 1. 数据预处理和关联关系建立
     * 2. 批量数据库操作（减少数据库交互次数）
     * 3. 异步后处理（购物车清理、状态同步）
     *
     * 性能提升：
     * - 批量插入替代逐个插入
     * - 预计算统计字段，避免重复计算
     * - 优化事务边界，提升并发性能
     * - 减少数据库往返次数
     * </pre>
     *
     * @param orderContextDTO 订单上下文
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPurchaseOrder(OrderContextDTO orderContextDTO) {
        TzOrderPurchase purchaseOrder = orderContextDTO.getPurchaseOrder();
        List<TzOrderSupplier> supplierOrders = orderContextDTO.getSupplierOrders();
        List<TzOrderItem> orderItems = orderContextDTO.getOrderItems();

        log.info("开始创建订单，买家ID: {}, 采购的供应商数量: {} , sku 数量: {}",
            purchaseOrder.getBuyerId(), supplierOrders.size(), orderItems.size());

        try {
            // 执行核心订单创建逻辑
            executeOrderCreationCore(purchaseOrder, supplierOrders, orderItems);

            // 清理购物车
            cleanupShoppingCartAsync(orderContextDTO.getShoppingCartIds());

            log.info("订单创建成功，采购订单号: {}, 供应商数量: {}, 商品项数: {}, 总金额: {}",
                purchaseOrder.getPurchaseOrderNo(), supplierOrders.size(), orderItems.size(), purchaseOrder.getCustomerTotalAmount());

        } catch (BusinessExceptionI18n e) {
            log.error("订单创建业务异常，买家ID: {}, 错误: {}", purchaseOrder.getBuyerId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("订单创建系统异常，买家ID: {}", purchaseOrder.getBuyerId(), e);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.VALIDATION_FAILED, e.getMessage());
        }
    }

    @Override
    public TzOrderPurchase getByIdAndIgnoreTenantId(Long purchaseOrderId) {
        return this.baseMapper.findByIdAndIgnoreTenantId(purchaseOrderId);
    }

    @Override
    public OrderContextDTO getOrderContextByPurchaseOrderId(Long purchaseOrderId) {
        return getOrderContextByPurchaseOrderNo(null, purchaseOrderId);
    }

    @Override
    public OrderContextDTO getOrderContextByPurchaseOrderNo(String purchaseOrderNo) {
        return getOrderContextByPurchaseOrderNo(purchaseOrderNo, null);
    }

    @Override
    public List<UserPurchaseOrderDTO> getUserPurchaseOrderByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        log.info("根据采购订单ID列表获取采购订单，买家ID: {}, 采购订单ID列表: {}", UserContextHolder.getUserId(), ids);

        // 1. 查询采购订单列表
        List<TzOrderPurchase> purchaseOrders = this.listByIds(ids);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            log.warn("未找到采购订单，ID列表: {}", ids);
            return List.of();
        }

        // 2. 查询相关的订单商品信息
        List<TzOrderItem> allOrderItems = orderItemMapper.selectList(
            new LambdaQueryWrapper<TzOrderItem>()
                .in(TzOrderItem::getPurchaseOrderId, ids)
                .orderByDesc(TzOrderItem::getGmtCreated)
        );

        // 3. 按采购订单ID分组订单商品
        Map<Long, List<TzOrderItem>> orderItemsMap = allOrderItems.stream()
            .collect(Collectors.groupingBy(TzOrderItem::getPurchaseOrderId));

        // 4. 转换为DTO
        return purchaseOrders.stream()
            .map(purchaseOrder -> {
                List<TzOrderItem> orderItems = orderItemsMap.getOrDefault(purchaseOrder.getId(), List.of());
                return UserPurchaseOrderDTOConvert.INSTANCE.convertToUserPurchaseOrderDTO(purchaseOrder, orderItems);
            })
            .toList();
    }

    /**
     * 根据采购订单号获取订单上下文
     *
     * @param purchaseOrderNo 采购订单号
     * @param purchaseOrderId 采购订单 Id
     * @return 订单上下文
     */
    private OrderContextDTO getOrderContextByPurchaseOrderNo(String purchaseOrderNo, Long purchaseOrderId) {
        // 查询采购订单
        TzOrderPurchase purchaseOrder = this.getOne(
            new LambdaQueryWrapper<TzOrderPurchase>()
                .eq(Objects.nonNull(purchaseOrderId), TzOrderPurchase::getId, purchaseOrderId)
                .eq(Objects.nonNull(purchaseOrderNo), TzOrderPurchase::getPurchaseOrderNo, purchaseOrderNo)
                .eq(TzOrderPurchase::getBuyerId, UserContextHolder.getUserId()));

        if (purchaseOrder == null) {
            log.warn("采购订单不存在，订单号: {}", purchaseOrderNo);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "purchase order");
        }

        // 查询供应商订单
        List<TzOrderSupplier> supplierOrders = orderSupplierMapper.selectList(
            new LambdaQueryWrapper<TzOrderSupplier>()
                .eq(TzOrderSupplier::getPurchaseOrderId, purchaseOrder.getId()));

        // 查询订单项
        List<TzOrderItem> orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<TzOrderItem>()
            .eq(TzOrderItem::getPurchaseOrderId, purchaseOrder.getId()));

        // 构建订单上下文
        return OrderContextDTO.builder()
            .purchaseOrder(purchaseOrder)
            .supplierOrders(supplierOrders)
            .orderItems(orderItems)
            .build();
    }

    /**
     * 执行核心订单创建逻辑
     *
     * <pre>
     * 性能优化要点：
     * 1. 预处理所有关联关系和统计数据
     * 2. 使用批量操作减少数据库交互
     * 3. 一次性完成所有数据持久化
     * </pre>
     */
    private void executeOrderCreationCore(TzOrderPurchase purchaseOrder,
        List<TzOrderSupplier> supplierOrders,
        List<TzOrderItem> orderItems) {

        // 1. 预处理数据和建立关联关系
        OrderProcessingData processingData = preprocessOrderData(purchaseOrder, supplierOrders, orderItems);

        // 2. 批量保存所有订单数据
        batchSaveOrderData(processingData);

        log.info("核心订单数据创建完成 - 采购订单ID: {}, 供应商订单数: {}, 订单项数: {}",
            purchaseOrder.getId(), supplierOrders.size(), orderItems.size());
    }

    /**
     * 预处理订单数据，建立关联关系和计算统计字段
     *
     * @return 处理完成的订单数据
     */
    private OrderProcessingData preprocessOrderData(TzOrderPurchase purchaseOrder,
        List<TzOrderSupplier> supplierOrders,
        List<TzOrderItem> orderItems) {

        // 1. 初始化采购订单
        initializePurchaseOrderWithStatistics(purchaseOrder, supplierOrders, orderItems);

        return new OrderProcessingData(purchaseOrder, supplierOrders, orderItems);
    }

    /**
     * 批量保存订单数据
     */
    private void batchSaveOrderData(OrderProcessingData data) {
        // 1. 保存采购订单
        this.save(data.getPurchaseOrder());
        log.debug("采购订单保存成功，订单ID: {}", data.getPurchaseOrder().getId());

        // 2. 批量插入供应商订单
        if (!CollectionUtils.isEmpty(data.getSupplierOrders())) {
            orderSupplierMapper.insertBatch(data.getSupplierOrders());
            log.debug("供应商订单批量保存成功，数量: {}", data.getSupplierOrders().size());
        }

        // 3. 批量插入订单项
        if (!CollectionUtils.isEmpty(data.getOrderItems())) {
            orderItemMapper.insertBatch(data.getOrderItems());
            log.debug("订单项批量保存成功，数量: {}", data.getOrderItems().size());
        }
    }

    /**
     * 初始化采购订单并预计算统计字段
     */
    private void initializePurchaseOrderWithStatistics(TzOrderPurchase purchaseOrder,
        List<TzOrderSupplier> supplierOrders,
        List<TzOrderItem> orderItems) {
        // 设置默认初始状态
        purchaseOrder.setOrderStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING);

        // 预计算并设置统计字段
        purchaseOrder.setSupplierCount(supplierOrders.size());
        purchaseOrder.setLineItemCount(orderItems.size());
        purchaseOrder.setCompletedSupplierCount(0);

        // 设置状态管理字段
        LocalDateTime now = LocalDateTime.now();
        purchaseOrder.setOrderDate(now);

        log.debug("采购订单统计字段初始化完成，供应商数量: {}, 订单项数量: {}",
            supplierOrders.size(), orderItems.size());
    }

    /**
     * 异步清理购物车
     */
    private void cleanupShoppingCartAsync(List<Long> shoppingCartIds) {
        if (CollectionUtils.isEmpty(shoppingCartIds)) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                shoppingCartMapper.deleteByIds(shoppingCartIds);
                log.info("购物车异步清理完成，删除数量: {}", shoppingCartIds.size());
            } catch (Exception e) {
                log.warn("购物车异步清理失败，购物车IDs: {}, 错误: {}", shoppingCartIds, e.getMessage());
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 订单处理数据封装类
     */
    @Getter
    private static class OrderProcessingData {

        private final TzOrderPurchase purchaseOrder;
        private final List<TzOrderSupplier> supplierOrders;
        private final List<TzOrderItem> orderItems;

        public OrderProcessingData(TzOrderPurchase purchaseOrder,
            List<TzOrderSupplier> supplierOrders,
            List<TzOrderItem> orderItems) {
            this.purchaseOrder = purchaseOrder;
            this.supplierOrders = supplierOrders;
            this.orderItems = orderItems;
        }

    }

}
