/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.impl;

import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.fulfillmen.shop.domain.req.FreightEstimateReq;
import com.fulfillmen.support.alibaba.api.request.logistics.ProductFreightEstimateRequestRecord;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import com.fulfillmen.support.alibaba.service.ILogisticsService;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageUploadRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsKeywordNavigationRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRelatedRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSellerRequestRecord;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse.ProductDetail;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse.SearchResult;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse.NavigationCategory;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse.NavigationResult;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRelatedRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse.PageInfo;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse.Result;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import com.fulfillmen.support.alibaba.service.IGoodsService;
import lombok.extern.slf4j.Slf4j;

/**
 * 1688 商品管理
 *
 * <pre>
 * 1. 获取商品详情信息
 * 2. 搜索商品列表
 * 3. 上传图片获取图片ID
 * 4. 通过图片ID搜索相似商品
 * 5. 通过图片搜索相似商品(上传图片并搜索)
 * 6. 获取卖家店铺的商品列表
 * 7. 获取关键词导航
 * 8. 根据商品ID获取推荐商品(相似商品推荐)
 * 9. 获取相关推荐商品
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Slf4j
@Component
public class ProductManager implements IProductManager {

    private static final String SUCCESS_CODE = "200";
    private final IGoodsService goodsService;
    private final ILogisticsService logisticsService;

    public ProductManager(IGoodsService goodsService, ILogisticsService logisticsService) {
        this.goodsService = goodsService;
        this.logisticsService = logisticsService;
    }

    /**
     * 根据 offerId 获取商品详情信息。
     *
     * @param offerId 商品ID
     * @return 商品详情信息
     */
    @Override
    public GoodsDetailResponse.ProductDetail getProductDetail(Long offerId) {
        try {
            return goodsService.getGoodsDetail(new GoodsDetailRequestRecord(offerId, LanguageEnum.EN
                .getLanguage(), null)).<ProductDetail>handle((response, sink) -> {
                    if (response == null || response.getResult() == null) {
                        log.warn("get goods detail is empty , offerId: [{}] , response: [{}]", offerId, response);
                        sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED));
                        return;
                    }
                    GoodsDetailResponse.Result result = response.getResult();
                    log.debug("result: {}", result);
                    if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getCode())) {
                        log.error("get goods detail failed , offerId: [{}] , response: [{}]", offerId, response);
                        sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED));
                        return;
                    }
                    sink.next(result.getResult());
                }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("Failed to get goods detail for offerId: {}", offerId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED, offerId);
        }
    }

    /**
     * 搜索商品列表
     *
     * @param request 搜索请求参数
     * @return 搜索结果分页数据
     */
    @Override
    public GoodsSearchResponse.SearchResult searchProducts(ProductSearchRequestDTO request) {
        try {
            // 构建搜索请求
            GoodsSearchRequestRecord searchRequest = buildSearchRequest(request);
            // 执行搜索
            return goodsService.searchGoods(searchRequest).<GoodsSearchResponse.SearchResult>handle((response, sink) -> {
                if (response == null || response.getResult() == null) {
                    log.warn("search is empty , searchRequest : {}", searchRequest);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_SEARCH_FAILED));
                    return;
                }
                GoodsSearchResponse.Result result = response.getResult();
                log.debug("search result: {}", result);
                if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getCode())) {
                    log.error("search products failed , searchRequest: [{}] , response: [{}]", searchRequest, response);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_SEARCH_FAILED));
                    return;
                }
                sink.next(result.getResult());
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("Failed to search products with request: {}", request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_SEARCH_FAILED, request);
        }
    }

    /**
     * 构建商品搜索请求
     *
     * @param request 搜索请求 DTO
     * @return 商品搜索请求记录
     */
    private GoodsSearchRequestRecord buildSearchRequest(ProductSearchRequestDTO request) {
        // 构建排序参数
        String sort = null;
        if (request.getSortField() != null && request.getSortOrder() != null) {
            sort = String.format("{\"%s\":\"%s\"}", request.getSortField(), request.getSortOrder());
        }

        // 构建搜索请求
        return new GoodsSearchRequestRecord(request.getKeyword(), request.getLanguage() != null
            ? request.getLanguage().getLanguage()
            : LanguageEnum.EN.getLanguage(), request.getPageSize(), request.getFilter(), sort,
            // outMemberId
            null, request.getMinPrice(), request.getMaxPrice(), request.getCategoryId(), request.getCategoryIdList(),
            // regionOpp
            request.getRegionOpp(),
            // productCollectionId
            request.getProductCollectionId(),
            // snId
            request.getSnId(),
            // keywordTranslate
            request.getKeywordTranslate(),
            request.getPage(), null);
    }

    /**
     * 构建商品图片搜索请求
     *
     * <pre>
     * 支持的搜索参数映射：
     * 1. imageId -> imageId (图片ID，必填)
     * 2. imageUrl -> imageAddress (图片地址，可选)
     * 3. keyword -> keyword (关键词，可选)
     * 4. 分页参数：page, pageSize
     * 5. 筛选参数：filter, sort, price range, categoryId
     * 6. 其他参数：keywordTranslate等
     * </pre>
     *
     * @param request 搜索请求 DTO
     * @return 商品图片搜索请求记录
     */
    private GoodsImageSearchRequestRecord buildImageSearchRequest(ProductSearchRequestDTO request) {
        // 构建排序参数
        String sort = null;
        if (request.getSortField() != null && request.getSortOrder() != null) {
            sort = String.format("{\"%s\":\"%s\"}", request.getSortField(), request.getSortOrder());
        }

        // 使用工厂方法构建请求，支持完整的参数映射
        return GoodsImageSearchRequestRecord.of(
            request.getImageId(), // 图片ID
            request.getLanguage() != null ? request.getLanguage() : LanguageEnum.EN, // 语言
            request.getPageSize(), // 分页大小
            request.getPage(), // 页码
            request.getRegionOpp(), // 区域
            request.getFilter(), // 筛选参数
            sort, // 排序参数
            null, // outMemberId - 暂不支持
            request.getMinPrice(), // 最小价格
            request.getMaxPrice(), // 最大价格
            request.getCategoryId(), // 分类ID
            request.getImageUrl(), // 图片地址 - 新增支持
            request.getKeyword(), // 关键词
            null, // auxiliaryText - 暂不支持
            request.getProductCollectionId(), // 商品集合ID
            request.getKeywordTranslate() // 关键词翻译标识
        );
    }

    /**
     * 上传图片获取图片ID
     *
     * @param file 图片文件 文件不大于 2Mb
     * @return 图片ID
     */
    @Override
    public String uploadImage(MultipartFile file) {
        try {
            // 1. 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.INVALID_FILE_TYPE);
            }

            // 检查文件大小（8MB限制）
            if (file.getSize() > 8 * 1024 * 1024) {
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.FILE_SIZE_EXCEEDED);
            }
            byte[] imageBytes = FileCopyUtils.copyToByteArray(file.getInputStream());
            // 将文件转换为Base64
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // 2. 上传图片获取 imageId
            return goodsService.uploadImage(GoodsImageUploadRequestRecord.of(base64Image))
                .<String>handle((response, sink) -> {
                    if (response == null || response.getResult() == null) {
                        log.error("图片上传失败 , base64Image: [{}] , response: [{}]", base64Image, response);
                        sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.IMAGE_UPLOAD_FAILED));
                        return;
                    }
                    var uploadResult = response.getResult();
                    if (!"true".equals(uploadResult.getSuccess()) || !SUCCESS_CODE.equals(uploadResult.getCode())) {
                        log.error("图片上传失败 , base64Image: [{}] , response: [{}]", base64Image, response);
                        sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.IMAGE_UPLOAD_FAILED));
                        return;
                    }
                    sink.next(uploadResult.getResult());
                })
                .block();
        } catch (IOException e) {
            log.error("Failed to process image file", e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.IMAGE_PROCESS_FAILED);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("Failed to upload image", e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.IMAGE_UPLOAD_FAILED);
        }
    }

    /**
     * 通过图片ID 或 图片URL + 关键词搜索相似商品
     *
     * @param request 请求参数集合
     * @return 搜索结果
     */
    @Override
    public GoodsImageSearchResponse.SearchResult searchProductsByImage(AggregateSearchReq request) {
        log.debug("searchProductsByImage request: {}", request);
        try {
            // 1. 构建图片搜索请求
            // 构建ProductSearchRequestDTO用于复用现有逻辑
            ProductSearchRequestDTO searchRequest = ProductSearchRequestDTO.fromAggregateSearchReq(request);

            // 使用buildImageSearchRequest构建图片搜索请求
            GoodsImageSearchRequestRecord imageSearchRequest = buildImageSearchRequest(searchRequest);

            // 使用 imageId 进行相似商品的检索
            return goodsService.searchGoodsByImage(imageSearchRequest).<SearchResult>handle((response, sink) -> {
                if (response == null || response.getResult() == null) {
                    log.error("Failed to get search products by image , request: [{}] , response: [{}]", request, response);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_SEARCH_FAILED));
                    return;
                }
                var searchResult = response.getResult();
                if (!"true".equals(searchResult.getSuccess()) || !SUCCESS_CODE.equals(searchResult.getCode())) {
                    log.error("Failed to get search products by image , request: [{}] , response: [{}]", request, response);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_SEARCH_FAILED));
                    return;
                }
                sink.next(searchResult.getResult());
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("Failed to search products by image", e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_SEARCH_FAILED);
        }
    }

    /**
     * 通过图片搜索相似商品(上传图片并搜索)
     *
     * @param file     图片文件
     * @param language 语言
     * @param page     页码
     * @param pageSize 每页大小
     * @return 搜索结果
     */
    @Override
    public GoodsImageSearchResponse.SearchResult searchProductsByImage(MultipartFile file,
        LanguageEnum language,
        Integer page,
        Integer pageSize) {
        // 1. 上传图片获取 imageId
        String imageId = uploadImage(file);

        // 2. 使用 imageId 搜索商品
        return searchProductsByImage(AggregateSearchReq.builder()
            .imageId(imageId)
            .page(page)
            .pageSize(pageSize)
            .build());
    }

    /**
     * 获取卖家店铺的商品列表
     *
     * @param country      国家
     * @param sellerOpenId 卖家ID
     * @param page         页码
     * @param pageSize     每页大小
     */
    @Override
    public GoodsSellerResponse.PageInfo getSellerProducts(LanguageEnum country,
        String sellerOpenId,
        Integer page,
        Integer pageSize) {
        GoodsSellerRequestRecord sellerRequest = GoodsSellerRequestRecord.of(country, sellerOpenId, page, pageSize);
        try {
            return goodsService.getSellerGoods(sellerRequest).<PageInfo>handle((response, sink) -> {
                if (Objects.isNull(response) || Objects.isNull(response.getResult())) {
                    log.warn("Failed to get seller products - empty response. Request: {}", sellerRequest);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SELLER_PRODUCTS_GET_FAILED));
                    return;
                }

                Result result = response.getResult();
                if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getCode())) {
                    log.error("seller goods request failed.  request: {} , response: {}", sellerRequest, response);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SELLER_PRODUCTS_GET_FAILED));
                    return;
                }
                log.debug("Successfully retrieved seller products. Result count: {}", result.getResult()
                    .getTotalRecords());
                sink.next(result.getResult());
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("Failed to get seller products with request: {}", sellerRequest, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SELLER_PRODUCTS_GET_FAILED);
        }
    }

    /**
     * 获取关键词导航
     * <pre>
     * 通过关键词搜索商品，并返回关键词导航分类
     * </pre>
     *
     * @param country  国家
     * @param keyword  关键词
     * @param region   区域
     * @param currency 货币
     * @return List<GoodsKeywordNavigationResponse.NavigationCategory>
     */
    @Override
    public List<GoodsKeywordNavigationResponse.NavigationCategory> getKeywordNavigation(LanguageEnum country,
        String keyword,
        String region,
        String currency) {
        GoodsKeywordNavigationRequestRecord keywordRequest = GoodsKeywordNavigationRequestRecord
            .of(keyword, country, region, currency);
        try {
            return goodsService.getKeywordNavigation(keywordRequest)
                .<List<NavigationCategory>>handle((response, sink) -> {
                    if (response == null || response.getResult() == null) {
                        log.warn("search is empty , keywordRequest : {}", keywordRequest);
                        sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.KEYWORD_NAVIGATION_GET_FAILED));
                        return;
                    }
                    NavigationResult result = response.getResult();
                    log.debug("keyword navigation result: {}", result);
                    if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getRetCode())) {
                        log.error("keywordNavigation goods request failed. request: {} , response: {}", keywordRequest, response);
                        sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.KEYWORD_NAVIGATION_GET_FAILED));
                        return;
                    }
                    sink.next(result.getResult());
                })
                .block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("Failed to get keyword navigation with request: {}", keywordRequest, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.KEYWORD_NAVIGATION_GET_FAILED);
        }
    }

    /**
     * 获取推荐商品
     *
     * @param country  国家
     * @param page     页码
     * @param pageSize 每页大小
     * @return 推荐商品列表
     */
    @Override
    public List<GoodsRecommendResponse.ProductInfo> getRecommendProducts(LanguageEnum country,
        Integer page,
        Integer pageSize) {
        PaginationParams pagination = validateAndNormalizePagination(page, pageSize);
        GoodsRecommendRequestRecord recommendRequest = GoodsRecommendRequestRecord.of(country, pagination
            .page(), pagination.pageSize());
        return goodsService.recommendGoods(recommendRequest).<List<GoodsRecommendResponse.ProductInfo>>handle((response, sink) -> {
            if (Objects.isNull(response) || Objects.isNull(response.getResult())) {
                log.warn("Failed to get recommend products - empty response. Request: {}", recommendRequest);
                sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RECOMMEND_PRODUCTS_GET_FAILED));
                return;
            }
            GoodsRecommendResponse.Result result = response.getResult();
            if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getCode())) {
                log.error("Failed to get recommend products - empty response. Request: {}", recommendRequest);
                sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RECOMMEND_PRODUCTS_GET_FAILED));
                return;
            }
            sink.next(Arrays.asList(result.getResult()));
        }).block();
    }

    /**
     * 获取相关推荐商品(相似商品推荐)
     *
     * @param offerId  商品ID
     * @param country  国家
     * @param page     页码
     * @param pageSize 每页大小
     * @return 相关推荐商品列表
     */
    @Override
    public List<GoodsRelatedRecommendResponse.ProductInfo> getRelatedRecommend(Long offerId,
        LanguageEnum country,
        Integer page,
        Integer pageSize) {
        PaginationParams pagination = validateAndNormalizePagination(page, pageSize);
        GoodsRelatedRecommendRequestRecord relatedRecommendRequestRecord = GoodsRelatedRecommendRequestRecord
            .of(offerId, country, pagination.page(), pagination.pageSize());
        return goodsService.getRelatedRecommend(relatedRecommendRequestRecord).<List<GoodsRelatedRecommendResponse.ProductInfo>>handle((response, sink) -> {
            if (Objects.isNull(response) || Objects.isNull(response.getResult())) {
                log.warn("Failed to get related recommend products - empty response. Request: {}", relatedRecommendRequestRecord);
                sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RELATED_RECOMMEND_PRODUCTS_GET_FAILED));
                return;
            }
            GoodsRelatedRecommendResponse.Result result = response.getResult();
            if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getCode())) {
                log.error("Failed to get related recommend products - empty response. Request: {}", relatedRecommendRequestRecord);
                sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RELATED_RECOMMEND_PRODUCTS_GET_FAILED));
                return;
            }
            sink.next(Arrays.asList(result.getResult()));
        }).block();
    }

    @Override
    public ProductFreightEstimateResponse.ProductFreightModel estimateFreight(FreightEstimateReq req) {
        log.debug("开始调用1688运费估算API, 请求: {}", req);

        // 1. 构建1688 API请求对象
        ProductFreightEstimateRequestRecord requestRecord = new ProductFreightEstimateRequestRecord(
            req.getOfferId(),
            req.getToProvinceCode(),
            req.getToCityCode(),
            req.getToCountryCode(),
            req.getTotalNum(),
            req.getLogisticsSkuNumModels()
        );

        try {
            // 2. 调用底层物流服务
            return logisticsService.estimateFreight(requestRecord).<ProductFreightEstimateResponse.ProductFreightModel>handle((response, sink) -> {
                if (response == null || response.getResult() == null) {
                    log.warn("1688运费估算API返回为空, request: {}", requestRecord);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.FREIGHT_ESTIMATE_FAILED, req.getOfferId()));
                    return;
                }

                ProductFreightEstimateResponse.ApiResult result = response.getResult();
                // 检查接口调用是否成功
                if (!result.getSuccess() || !SUCCESS_CODE.equals(result.getCode())) {
                    log.error("1688运费估算API调用失败, request: {}, response: {}", requestRecord, response);
                    // 使用工厂方法创建异常，避免构造器问题
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.FREIGHT_ESTIMATE_FAILED, "1688接口调用失败"));
                    return;
                }

                ProductFreightEstimateResponse.ProductFreightModel freightModel = result.getResult();
                // 检查业务结果是否成功
                if (freightModel == null) {
                    log.warn("1688运费估算业务失败，返回结果为空, request: {}", requestRecord);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.FREIGHT_ESTIMATE_FAILED, "运费估算失败，请检查地址和购买数量"));
                    return;
                }

                log.info("1688运费估算API调用成功, offerId: {}, 是否包邮: {}",
                    req.getOfferId(), freightModel.getFreePostage());
                sink.next(freightModel);
            }).block();
        } catch (Exception e) {
            // 重新包装异常，保持异常类型一致性
            if (e instanceof BusinessExceptionI18n) {
                throw (BusinessExceptionI18n) e;
            }
            log.error("1688运费估算API调用异常, request: {}", requestRecord, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.FREIGHT_ESTIMATE_FAILED, String.valueOf(req.getOfferId()));
        }
    }

    private PaginationParams validateAndNormalizePagination(Integer page, Integer pageSize) {
        int validPage = Optional.ofNullable(page).filter(p -> p > 0).orElse(1);
        int validPageSize = Optional.ofNullable(pageSize).filter(ps -> ps > 0 && ps <= 100).orElse(20);
        return new PaginationParams(validPage, validPageSize);
    }

    /**
     * 统一分页参数校验和标准化
     */
    private record PaginationParams(
        int page,
        int pageSize
    ) {

    }
}
