/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.handler;

import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单事件处理器 - 优化版本
 *
 * <pre>
 * 职责：
 * 1. 接收和验证webhook消息
 * 2. 路由到相应的业务处理服务
 * 3. 统一的异常处理和日志记录
 * 4. 支持新旧版本数据的兼容处理
 *
 * 优化要点：
 * - 简化Handler职责，将业务逻辑委托给Service层
 * - 改进异常处理和日志记录
 * - 集成订单状态管理系统
 * - 支持数据补齐和兼容性处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/11 10:52
 * @description 订单webhook事件处理器，支持新旧版本数据兼容
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderHandler extends AbstractTypedMessageHandler<OrderMessage> {

    /**
     * 支持的消息类型
     */
    private static final CallbackMessageType[] ORDER_SUPPORTED_TYPES = List.of(
        /*
         * 订单创建
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE,
        /*
         * 订单付款
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PAY,
        /*
         * 批量订单付款
         */
        OrderMessageTypeEnums.ORDER_BATCH_PAY,
        /*
         * 订单发货
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS,
        /*
         * 订单部分发货
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_PART_PART_SENDGOODS,
        /*
         * 订单确认收货
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS,
        /*
         * 订单交易成功
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS,
        /*
         * 订单改价
         */
        OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY).toArray(new CallbackMessageType[0]);
    private final OrderWebhookService orderWebhookService;
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;

    public OrderHandler(OrderWebhookService orderWebhookService,
        SysAlibabaCallbackLogsRepository callbackLogsRepository) {
        // 构造器中指定支持的消息类型
        super(ORDER_SUPPORTED_TYPES);
        this.orderWebhookService = orderWebhookService;
        this.callbackLogsRepository = callbackLogsRepository;
    }

    @Override
    public List<CallbackMessageType> getSupportedTypes() {
        log.info("支持的消息类型: {}", super.getSupportedTypes());
        return super.getSupportedTypes();
    }

    @Override
    public boolean canHandle(CallbackMessageType messageType) {
        return super.canHandle(messageType);
    }

    @Override
    protected void doHandle(OrderMessage data, MessageEvent<OrderMessage> event) {
        String messageType = event.getType().getMessageType();
        String orderId = String.valueOf(data.getOrderId());
        String msgId = event.getMsgId();

        Long logId = null;
        try {
            log.info("接收到订单webhook消息: msgId={}, type={}, orderId={}, status={}",
                msgId, messageType, orderId, data.getCurrentStatus());

            // 1. 验证消息类型
            OrderMessageTypeEnums orderMessageTypeEnums = OrderMessageTypeEnums.fromMessageType(messageType);
            if (orderMessageTypeEnums == null) {
                log.warn("不支持的订单消息类型: messageType={}, orderId={}", messageType, orderId);
                return;
            }

            // 1.1 分发前创建 PROCESSING 日志（带事件与订单信息）
            logId = callbackLogsRepository.createProcessingLog(JacksonUtil.toJsonString(event), null, messageType,
                data.getOrderId(),
              event.getReceivedAt());

            // 2. 委托给业务服务处理
            orderWebhookService.processOrderWebhook(data, event, orderMessageTypeEnums);

            log.info("订单webhook消息处理完成: msgId={}, type={}, orderId={}", msgId, messageType, orderId);
            // 异步标记成功
            callbackLogsRepository.markSuccess(logId);

        } catch (IllegalArgumentException e) {
            log.error("订单webhook消息参数错误: msgId={}, type={}, orderId={}, error={}",
                msgId, messageType, orderId, e.getMessage());
            callbackLogsRepository.markFailed(logId, e.getMessage());
            // 参数错误不重试
        } catch (Exception e) {
            log.error("订单webhook消息处理失败: msgId={}, type={}, orderId={}, error={}",
                msgId, messageType, orderId, e.getMessage(), e);
            callbackLogsRepository.markFailed(logId, e.getMessage());
            // 系统错误，可能需要重试或人工介入
            throw e;
        }
    }

    @Override
    protected Class<OrderMessage> getDataClass() {
        return OrderMessage.class;
    }
}
