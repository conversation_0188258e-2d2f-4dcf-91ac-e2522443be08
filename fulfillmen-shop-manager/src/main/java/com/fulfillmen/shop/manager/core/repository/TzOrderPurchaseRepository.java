/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.domain.dto.order.UserPurchaseOrderDTO;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import java.util.List;
import java.util.Set;

/**
 * 订单仓储层
 *
 * <AUTHOR>
 * @date 2025/6/30 14:49
 * @description: 订单数据访问层接口，负责订单相关的数据持久化操作
 * @since 1.0.0
 */
public interface TzOrderPurchaseRepository extends IRepository<TzOrderPurchase> {

    /**
     * 创建采购订单
     *
     * @param orderContextDTO 订单上下文
     */
    void createPurchaseOrder(OrderContextDTO orderContextDTO);

    /**
     * 根据采购订单ID获取采购订单
     * <pre>
     * 忽略租户ID, 查询订单主键
     * </pre>
     *
     * @param purchaseOrderId 采购订单ID
     * @return 采购订单
     */
    TzOrderPurchase getByIdAndIgnoreTenantId(Long purchaseOrderId);

    /**
     * 根据采购订单ID获取订单上下文
     *
     * @param purchaseOrderId 采购订单ID
     * @return 订单上下文
     */
    OrderContextDTO getOrderContextByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 根据采购订单号获取采购订单
     *
     * @param purchaseOrderNo 采购订单号
     * @return 采购订单
     */
    OrderContextDTO getOrderContextByPurchaseOrderNo(String purchaseOrderNo);

    /**
     * 根据采购订单ID列表获取采购订单
     *
     * @param ids 采购订单ID列表
     * @return 采购订单列表
     */
    List<UserPurchaseOrderDTO> getUserPurchaseOrderByIds(Set<Long> ids);
}
