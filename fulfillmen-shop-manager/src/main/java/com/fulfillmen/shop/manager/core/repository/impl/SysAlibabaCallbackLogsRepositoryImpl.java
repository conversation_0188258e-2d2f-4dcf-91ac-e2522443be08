/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.domain.entity.enums.AlibabaCallbackLogsProcessStatusEnum;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/8/8 09:36
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Repository
public class SysAlibabaCallbackLogsRepositoryImpl
  extends CrudRepository<SysAlibabaCallbackLogsMapper, SysAlibabaCallbackLogs>
  implements SysAlibabaCallbackLogsRepository {

    private static final int MAX_METADATA_LEN = 4000;

    @Override
    public Long createProcessingLog(String message, String signature, String eventType, Long orderId,
      LocalDateTime receivedAt) {
        try {
            IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
            Long id = idGenerator.generate();

            LocalDateTime now = LocalDateTime.now();
            SysAlibabaCallbackLogs entity = SysAlibabaCallbackLogs.builder()
              .id(id)
              .metadata(message)
              .eventType(eventType)
              .orderId(orderId)
              .receivedTimestamp(receivedAt != null ? receivedAt : now)
              .processStatus(AlibabaCallbackLogsProcessStatusEnum.PROCESSING)
              .processFailedMsg(null)
              .sign(signature)
              .build();

            if (this.save(entity)) {
                log.debug("创建Webhook处理日志成功(含事件与订单): id={}, eventType={}, orderId={} ", id, eventType, orderId);
                return id;
            }
            log.warn("创建Webhook处理日志失败(含事件与订单)，返回0行");
        } catch (Exception e) {
            log.error("创建Webhook处理日志异常(含事件与订单)", e);
        }
        return null;
    }

    @Async
    @Override
    public void markSuccess(Long logId) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
              .id(logId)
              .processStatus(AlibabaCallbackLogsProcessStatusEnum.SUCCESS)
              .build();
            this.updateById(update);
        } catch (Exception e) {
            log.error("标记Webhook日志成功状态失败: id={}", logId, e);
        }
    }

    @Async
    @Override
    public void markFailed(Long logId, String reason) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
              .id(logId)
              .processStatus(AlibabaCallbackLogsProcessStatusEnum.FAILED)
              .processFailedMsg(reason)
              .build();
            this.updateById(update);
        } catch (Exception e) {
            log.error("标记Webhook日志失败状态失败: id={}", logId, e);
        }
    }

    @Override
    public List<SysAlibabaCallbackLogs> findFailedLogsForRetry(int maxRetryCount, int timeHours, int limit) {
        LocalDateTime timeThreshold = LocalDateTime.now().minusHours(timeHours);

        LambdaQueryWrapper<SysAlibabaCallbackLogs> wrapper = Wrappers.<SysAlibabaCallbackLogs>lambdaQuery()
          .eq(SysAlibabaCallbackLogs::getProcessStatus, AlibabaCallbackLogsProcessStatusEnum.FAILED)
          .lt(SysAlibabaCallbackLogs::getRetryCount, maxRetryCount)
          .ge(SysAlibabaCallbackLogs::getGmtCreated, timeThreshold)
          .isNotNull(SysAlibabaCallbackLogs::getOrderId)
          .orderByAsc(SysAlibabaCallbackLogs::getGmtCreated)
          .last("LIMIT " + limit);

        try {
            return this.list(wrapper);
        } catch (Exception e) {
            log.error("查询需要重试的失败记录异常: maxRetryCount={}, timeHours={}, limit={}",
              maxRetryCount, timeHours, limit, e);
            return List.of();
        }
    }

    @Override
    public void updateRetryCount(Long logId, int retryCount) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
              .id(logId)
              .retryCount(retryCount)
              .processStatus(AlibabaCallbackLogsProcessStatusEnum.PROCESSING)
              // 清除之前的失败信息
              .processFailedMsg(null)
              .gmtModified(LocalDateTime.now())
              .build();
            this.updateById(update);
            log.debug("更新重试次数成功: logId={}, retryCount={}", logId, retryCount);
        } catch (Exception e) {
            log.error("更新重试次数失败: logId={}, retryCount={}", logId, retryCount, e);
        }
    }

    @Override
    public SysAlibabaCallbackLogs findLatestByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }

        LambdaQueryWrapper<SysAlibabaCallbackLogs> wrapper = Wrappers.<SysAlibabaCallbackLogs>lambdaQuery()
          .eq(SysAlibabaCallbackLogs::getOrderId, orderId)
          .orderByDesc(SysAlibabaCallbackLogs::getGmtCreated)
          .last("LIMIT 1");

        try {
            return this.getOne(wrapper);
        } catch (Exception e) {
            log.error("根据订单ID查找最新回调记录异常: orderId={}", orderId, e);
            return null;
        }
    }

    @Override
    public long countFailedLogs(int timeHours) {
        LocalDateTime timeThreshold = LocalDateTime.now().minusHours(timeHours);

        LambdaQueryWrapper<SysAlibabaCallbackLogs> wrapper = Wrappers.<SysAlibabaCallbackLogs>lambdaQuery()
          .eq(SysAlibabaCallbackLogs::getProcessStatus, AlibabaCallbackLogsProcessStatusEnum.FAILED)
          .ge(SysAlibabaCallbackLogs::getGmtCreated, timeThreshold);

        try {
            return this.count(wrapper);
        } catch (Exception e) {
            log.error("统计失败记录数量异常: timeHours={}", timeHours, e);
            return 0L;
        }
    }
}
