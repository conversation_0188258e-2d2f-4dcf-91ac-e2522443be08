/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba;

import java.util.List;
import com.fulfillmen.shop.domain.req.FreightEstimateReq;

import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import org.springframework.web.multipart.MultipartFile;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse.ProductDetail;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsKeywordNavigationResponse.NavigationCategory;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse.ProductInfo;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRelatedRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse.SearchResult;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse.PageInfo;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;

/**
 * 1688 商品管理接口
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface IProductManager {

    /**
     * 根据 offerId 获取商品详情信息。
     *
     * @param offerId 商品ID
     * @return 商品详情信息
     */
    ProductDetail getProductDetail(Long offerId);

    /**
     * 搜索商品列表
     *
     * @param request 搜索请求参数
     * @return 商品列表
     */
    SearchResult searchProducts(ProductSearchRequestDTO request);

    /**
     * 上传图片获取图片ID
     *
     * @param file 图片文件
     * @return 图片ID
     */
    String uploadImage(MultipartFile file);

    /**
     * 通过图片ID 或 图片URL + 关键词搜索相似商品
     *
     * @param request 请求参数集合
     * @return 搜索结果
     */
    GoodsImageSearchResponse.SearchResult searchProductsByImage(AggregateSearchReq request);

    /**
     * 通过图片搜索相似商品(上传图片并搜索)
     * <pre>
     * 注意：无法返回 imageId。这样检索有问题
     * </pre>
     *
     * @param file     图片文件
     * @param language 语言
     * @param page     页码
     * @param pageSize 每页数量
     * @return 相似商品列表
     */
    GoodsImageSearchResponse.SearchResult searchProductsByImage(MultipartFile file,
        LanguageEnum language,
        Integer page,
        Integer pageSize);

    /**
     * 获取卖家店铺的商品列表
     *
     * @param country      国家
     * @param sellerOpenId 卖家ID
     * @param page         页码
     * @param pageSize     每页数量
     * @return 商品列表
     */
    PageInfo getSellerProducts(LanguageEnum country,
        String sellerOpenId,
        Integer page,
        Integer pageSize);

    /**
     * 获取关键词导航
     *
     * @param country  国家
     * @param keyword  关键词
     * @param region   地区
     * @param currency 货币
     * @return 关键词导航列表
     */
    List<NavigationCategory> getKeywordNavigation(LanguageEnum country,
        String keyword,
        String region,
        String currency);

    /**
     * 获取推荐商品
     *
     * @param country  国家
     * @param page     页码
     * @param pageSize 每页数量
     * @return 推荐商品列表
     */
    List<ProductInfo> getRecommendProducts(LanguageEnum country, Integer page, Integer pageSize);

    /**
     * 获取相关推荐商品(相似商品推荐)
     *
     * @param offerId  商品ID
     * @param country  国家
     * @param page     页码
     * @param pageSize 每页大小
     * @return 相关推荐商品列表
     */
    List<GoodsRelatedRecommendResponse.ProductInfo> getRelatedRecommend(Long offerId,
        LanguageEnum country,
        Integer page,
        Integer pageSize);

    /**
     * 估算商品运费
     *
     * @param req 运费估算请求参数
     * @return 1688运费估算结果
     */
    ProductFreightEstimateResponse.ProductFreightModel estimateFreight(FreightEstimateReq req);

}
