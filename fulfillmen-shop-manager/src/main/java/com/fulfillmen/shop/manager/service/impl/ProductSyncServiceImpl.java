/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.util.MetaInfoHashUtils;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.core.repository.TzProductSkuRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品同步服务实现 - 重构为门面模式
 *
 * <pre>
 * 架构优化：
 * 1. 门面模式：统一外部接口，隐藏内部复杂性
 * 2. 策略模式：根据配置选择不同的同步策略
 * 3. 职责分离：数据访问由Repository负责，同步逻辑由Strategy负责
 * 4. 扩展性：支持多平台接入和多种同步策略
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/06/19
 * @since 2.0.0
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor
public class ProductSyncServiceImpl implements IProductSyncService {

    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSkuRepository tzProductSkuRepository;
    private final TzProductSpuMapper tzProductSpuMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductDTO syncProductByPlatformId(String platformProductId) {
        log.debug("开始同步产品数据，platformProductId: {}", platformProductId);

        try {
            // 1. 检查SPU是否已存在（基于平台产品ID 或 PdcProductMapping ID）
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);
            Long productId = Long.valueOf(platformProductId);
            CompletableFuture<AlibabaProductDetailDTO> productDetailDTOCompletableFuture = CompletableFuture
              .supplyAsync(() -> this.pdcProductMappingRepository.getProductDetailWithCache(productId, false),
                threadPoolTaskExecutor);
            if (existingSpu != null) {
                log.debug("SPU已存在（基于平台产品ID），直接返回，spuId: {}", existingSpu.getId());
                // 获取现有的SKU列表
                List<TzProductSku> existingSkuList = getSkuListBySpuId(existingSpu.getId());
                // 如果不存在，则创建一个
                if (CollectionUtil.isEmpty(existingSkuList)) {
                    log.warn("SKU列表为空，platformProductId: {}", platformProductId);
                    // 单品，创建一个默认SKU
                    TzProductSku defaultSku = TzProductMapping.INSTANCE
                      .toSingleItemSku(productDetailDTOCompletableFuture.get(), existingSpu.getId());
                    if (defaultSku != null) {
                        tzProductSkuRepository.save(defaultSku);
                        existingSkuList.add(defaultSku);
                        log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
                    }
                }
                // 转换为DTO返回
                return TzProductMapping.INSTANCE
                  .toTzProductDTO(existingSpu, existingSkuList, productDetailDTOCompletableFuture.get());
            }
            AlibabaProductDetailDTO productDetail = productDetailDTOCompletableFuture.get();
            // 2. 从PdcProductMapping获取产品详情
            if (productDetail == null) {
                log.warn("未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 3. 使用 Mapstruct 创建SPU
            TzProductSpu spu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

            try {
                tzProductSpuMapper.insert(spu);
                log.debug("SPU创建成功，spuId: {}", spu.getId());
            } catch (Exception e) {
                log.error("SPU创建失败，platformProductId: {}", platformProductId, e);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
            }

            // 5. 创建SKU
            List<TzProductSku> skuList = new ArrayList<>();
            if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
                // 多规格产品，创建多个SKU
                skuList = TzProductMapping.INSTANCE.toTzProductSkuList(productDetail.getProductSkuList(), spu
                  .getId(), productDetail.getPlatformProductId(), spu.getMinOrderQuantity());

                if (CollectionUtil.isNotEmpty(skuList)) {
                    tzProductSkuRepository.batchInsertSkus(skuList);
                    log.debug("多规格SKU创建成功，数量: {}", skuList.size());
                }
            } else {
                // 单品，创建一个默认SKU
                TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spu.getId());
                if (defaultSku != null) {
                    tzProductSkuRepository.save(defaultSku);
                    skuList.add(defaultSku);
                    log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
                }
            }

            // 7. 转换为DTO返回
            return TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, productDetail);
        } catch (Exception e) {
            if (e instanceof BusinessExceptionI18n) {
                throw (BusinessExceptionI18n) e;
            }
            log.error("同步产品数据失败，platformProductId: {}", platformProductId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
        }
    }

    @Override
    public List<TzProductSku> getSkuListBySpuId(Long spuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getSpuId, spuId);
        return tzProductSkuRepository.list(queryWrapper);
    }

    @Override
    public boolean isSingleItem(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
              .getProductDetailWithCache(productId, false);
            return productDetail != null && productDetail.isSingleItem();
        } catch (Exception e) {
            log.warn("检查产品是否为单品失败，platformProductId: {}", platformProductId, e);
            return false;
        }
    }

    @Override
    public TzProductSku getSkuByPlatformIds(String platformProductId, String platformSkuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getPlatformProductId, platformProductId)
          .eq(TzProductSku::getPlatformSku, platformSkuId);
        return tzProductSkuRepository.getOne(queryWrapper);
    }

    @Override
    public BigDecimal getSingleItemPrice(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
              .getProductDetailWithCache(productId, false);
            if (productDetail == null) {
                log.warn("获取单品价格失败，未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 使用 Mapstruct 中的价格提取逻辑
            return TzProductMapping.INSTANCE.extractDropShippingPriceFromProductDetail(productDetail);
        } catch (Exception e) {
            log.error("获取单品价格异常，platformProductId: {}", platformProductId, e);
            return null;
        }
    }

    @Override
    public TzProductSku getSingleItemDefaultSku(Long spuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        // 单品只有一个SKU
        queryWrapper.eq(TzProductSku::getSpuId, spuId).last("LIMIT 1");
        return tzProductSkuRepository.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductSku forceCreateDefaultSkuForSingleItem(Long spuId, String platformProductId) {
        log.warn("强制为单品创建默认SKU，spuId: {}, platformProductId: {}", spuId, platformProductId);

        try {
            // 1. 检查是否已存在默认SKU
            TzProductSku existingSku = getSingleItemDefaultSku(spuId);
            if (existingSku != null) {
                log.debug("单品默认SKU已存在，直接返回，skuId: {}", existingSku.getId());
                return existingSku;
            }

            // 2. 获取SPU信息
            TzProductSpu spu = tzProductSpuMapper.selectById(spuId);
            if (spu == null) {
                log.error("SPU不存在，无法创建默认SKU，spuId: {}", spuId);
                return null;
            }

            // 3. 从PdcProductMapping获取产品详情
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
              .getProductDetailWithCache(productId, false);
            if (productDetail == null) {
                log.warn("未找到产品详情，使用基础信息创建默认SKU，platformProductId: {}", platformProductId);
                // 使用基础信息创建默认SKU
                return createEmergencyDefaultSku(spuId, platformProductId, spu);
            }

            // 4. 使用 Mapstruct 创建默认SKU
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                tzProductSkuRepository.save(defaultSku);
                log.info("强制创建单品默认SKU成功，spuId: {}, skuId: {}", spuId, defaultSku.getId());
                return defaultSku;
            } else {
                log.error("创建单品默认SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId);
                return null;
            }
        } catch (Exception e) {
            log.error("强制创建单品默认SKU异常，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductDTO resyncProductByPlatformId(String platformProductId, boolean forceUpdate) {
        log.debug("重新同步产品数据，platformProductId: {}, forceUpdate: {}", platformProductId, forceUpdate);

        try {
            Long productId = Long.valueOf(platformProductId);
            CompletableFuture<AlibabaProductDetailDTO> productDetailDTOCompletableFuture = CompletableFuture
              .supplyAsync(() -> this.pdcProductMappingRepository.getProductDetailWithCache(productId, false),
                threadPoolTaskExecutor);
            // 1. 检查现有SPU
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);

            // 检查是否需要重新同步数据
            // 如果PdcProductMapping最后更新时间已经过去3天以上，则需要重新同步一次，防止库存数量不准确
            boolean needResync = checkIfNeedResync(platformProductId);
            if (needResync) {
                log.info("检测到数据已过期超过3天，触发重新同步: platformProductId={}", platformProductId);
                // 强制重新同步
                return resyncProductByPlatformId(platformProductId, true);
            }

            if (existingSpu != null && !forceUpdate) {
                log.debug("SPU已存在且不强制更新，直接返回，spuId: {}", existingSpu.getId());
                // 获取现有的SKU列表
                List<TzProductSku> existingSkuList = getSkuListBySpuId(existingSpu.getId());
                AlibabaProductDetailDTO alibabaProductDetailDTO = productDetailDTOCompletableFuture.get();
                // 转换为DTO返回
                return TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, existingSkuList, alibabaProductDetailDTO);
            }

            // 2. 从PdcProductMapping获取最新产品详情
            // 强制刷新缓存
            AlibabaProductDetailDTO productDetail = productDetailDTOCompletableFuture.get();
            if (productDetail == null) {
                log.error("未找到产品详情，无法重新同步，platformProductId: {}", platformProductId);
                return null;
            }
            // 3. SPU 不为空，更新现有SPU
            if (Objects.nonNull(existingSpu)) {
                // 3. 更新现有SPU - 使用 LambdaUpdateWrapper 避免乐观锁问题
                log.info("更新现有SPU，spuId: {}", existingSpu.getId());
                TzProductSpu updatedSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

                // 使用 existingSpu 覆盖更新部分字段
                existingSpu.setTitle(updatedSpu.getTitle());
                existingSpu.setTitleTrans(updatedSpu.getTitleTrans());
                existingSpu.setDescription(updatedSpu.getDescription());
                existingSpu.setName(updatedSpu.getName());
                existingSpu.setNameTrans(updatedSpu.getNameTrans());
                existingSpu.setCategoryId(updatedSpu.getCategoryId());
                existingSpu.setCategoryName(updatedSpu.getCategoryName());
                existingSpu.setCategoryNameTrans(updatedSpu.getCategoryNameTrans());
                existingSpu.setMainImage(updatedSpu.getMainImage());
                existingSpu.setWhiteImage(updatedSpu.getWhiteImage());
                existingSpu.setImages(updatedSpu.getImages());
                existingSpu.setUnit(updatedSpu.getUnit());
                existingSpu.setUnitTrans(updatedSpu.getUnitTrans());
                existingSpu.setMainVideo(updatedSpu.getMainVideo());
                existingSpu.setDetailVideo(updatedSpu.getDetailVideo());
                existingSpu.setAttributeCpvs(updatedSpu.getAttributeCpvs());
                existingSpu.setShippingInfo(updatedSpu.getShippingInfo());
                existingSpu.setSkuShippingDetails(updatedSpu.getSkuShippingDetails());
                existingSpu.setMinOrderQuantity(updatedSpu.getMinOrderQuantity());
                existingSpu.setIsSingleItem(updatedSpu.getIsSingleItem());
                existingSpu.setPdcProductMappingId(updatedSpu.getPdcProductMappingId());
                existingSpu.setSourcePlatformSellerOpenId(updatedSpu.getSourcePlatformSellerOpenId());
                existingSpu.setSourcePlatformSellerName(updatedSpu.getSourcePlatformSellerName());

                int updateResult = tzProductSpuMapper.updateById(existingSpu);
                if (updateResult > 0) {
                    log.debug("SPU更新成功，spuId: {}", existingSpu.getId());
                } else {
                    log.warn("SPU更新失败，spuId: {}", existingSpu.getId());
                    return null;
                }

                // 4. 删除现有SKU
                LambdaQueryWrapper<TzProductSku> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(TzProductSku::getSpuId, existingSpu.getId());
                if (this.tzProductSkuRepository.remove(deleteWrapper)) {
                    log.debug("删除现有SKU成功，spuId: {} ", existingSpu.getId());
                }

                // 5. 重新创建SKU
                List<TzProductSku> newSkuList = createSkusForSpuAndReturn(existingSpu.getId(), productDetail);

                // 6. 转换为DTO返回
                return TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, newSkuList, productDetail);
            } else {
                // 6. 创建新的SPU和SKU
                log.info("创建新的SPU，platformProductId: {}", platformProductId);
                return syncProductByPlatformId(platformProductId);
            }
        } catch (Exception e) {
            log.error("重新同步产品数据异常，platformProductId: {}, forceUpdate: {}", platformProductId, forceUpdate, e);
            return null;
        }
    }

    /**
     * 检查SPU是否已存在（基于平台产品ID 或 PdcProductMappingId）
     *
     * @param platformProductId 平台产品ID
     * @return SPU实体，不存在返回null
     */
    private TzProductSpu getExistingSpuByPlatformId(String platformProductId) {
        log.debug("检查SPU是否已存在，platformProductId: {}", platformProductId);
        // 1.1 优先通过主键查询
        TzProductSpu productSpu = this.tzProductSpuMapper.selectById(platformProductId);
        if (Objects.nonNull(productSpu)) {
            return productSpu;
        }
        // 1.2 再次尝试 通过 pdcProductMappingId 获取 spuId
        return CompletableFuture.supplyAsync(() -> {
              LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
              queryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(TzProductSpu::getPdcPlatformProductId, platformProductId);
              return tzProductSpuMapper.selectOne(queryWrapper);
          }, threadPoolTaskExecutor)
          // 1.3 再次尝试 通过 pdcProductMappingId 获取 spuId
          .thenApplyAsync(result -> {
              if (result != null) {
                  return result;
              }
              LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
              queryWrapper.eq(TzProductSpu::getIsPdcSync, PdcProductMappingSyncStatusEnum.SYNCED)
                .eq(TzProductSpu::getPdcProductMappingId, platformProductId);
              return tzProductSpuMapper.selectOne(queryWrapper);
          }, threadPoolTaskExecutor)
          // 异常处理
          .handleAsync((result, ex) -> {
              if (ex != null) {
                  log.error("查询SPU失败，platformProductId: {}", platformProductId, ex);
                  return null;
              }
              return result;
          }).join();
    }

    /**
     * 创建应急默认SKU（当产品详情不可用时）
     */
    private TzProductSku createEmergencyDefaultSku(Long spuId, String platformProductId, TzProductSpu spu) {
        try {
            long skuId = TzProductMapping.INSTANCE.generateId();
            TzProductSku emergencySku = TzProductSku.builder()
              .id(skuId)
              .spuId(spuId)
              .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
              .platformProductId(platformProductId)
              .platformSku(platformProductId)
              .sku("SKU_" + skuId)
              .barcode(String.valueOf(skuId))
              .image(spu.getMainImage())
              .price(BigDecimal.ZERO)
              .dropShippingPrice(BigDecimal.ZERO)
              .specs(List.of()) // 空规格
              .quantity(0)
              .build();

            this.tzProductSkuRepository.save(emergencySku);
            log.info("创建应急默认SKU成功，spuId: {}, skuId: {}", spuId, emergencySku.getId());
            return emergencySku;
        } catch (Exception e) {
            log.error("创建应急默认SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return null;
        }
    }

    /**
     * 为SPU创建SKU
     */
    private void createSkusForSpu(Long spuId, AlibabaProductDetailDTO productDetail) {

        if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
            // 多规格产品
            List<TzProductSku> skuList = TzProductMapping.INSTANCE.toTzProductSkuList(productDetail
                .getProductSkuList(), spuId, productDetail.getPlatformProductId(),
              productDetail.getMinOrderQuantity());

            if (CollectionUtil.isNotEmpty(skuList)) {
                this.tzProductSkuRepository.batchInsertSkus(skuList);
                log.debug("重新创建多规格SKU成功，数量: {}", skuList.size());
            }
        } else {
            // 单品
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                this.tzProductSkuRepository.save(defaultSku);
                log.debug("重新创建单品默认SKU成功，skuId: {}", defaultSku.getId());
            }
        }
    }

    /**
     * 重新同步SPU的SKU数据
     */
    private boolean resyncSkusForSpu(Long spuId, String platformProductId) {
        try {
            // 获取产品详情
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
              .getProductDetailWithCache(productId, true); // 强制刷新缓存
            if (productDetail == null) {
                log.error("无法获取产品详情，重新同步SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId);
                return false;
            }

            // 创建SKU
            createSkusForSpu(spuId, productDetail);
            return true;
        } catch (Exception e) {
            log.error("重新同步SKU数据异常，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return false;
        }
    }

    /**
     * 为SPU创建SKU并返回新的SKU列表
     */
    private List<TzProductSku> createSkusForSpuAndReturn(Long spuId, AlibabaProductDetailDTO productDetail) {
        createSkusForSpu(spuId, productDetail);
        return getSkuListBySpuId(spuId);
    }

    // ==================== 实现新的统一数据获取方法 ====================

    @Override
    public TzProductDTO getOrSyncProductByPlatformId(String platformProductId) {
        log.debug("获取或同步产品数据: platformProductId={}", platformProductId);

        try {
            // 1.1 优先通过主键查询
            // 步骤1：检查SPU是否存在
            TzProductSpu existingSpu = Optional.ofNullable(this.tzProductSpuMapper.selectById(platformProductId)).orElseGet(() -> getExistingSpuByPlatformId(platformProductId));
            if (existingSpu != null && !needResync(existingSpu)) {
                log.debug("从现有SPU获取产品数据: spuId={}", existingSpu.getId());
                return buildTzProductDTOFromSpu(existingSpu, platformProductId);
            }

            // 步骤2：检查PdcProductMapping映射关系
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository.getProductDetailWithCache(productId,
              false);
            if (productDetail != null) {
                log.debug("从PdcProductMapping同步产品数据: platformProductId={}", platformProductId);
                return syncFromProductDetail(productDetail);
            }

            // 步骤3：通过API接口获取数据
            log.debug("通过API接口获取产品数据: platformProductId={}", platformProductId);
            return syncFromApiCall(platformProductId);

        } catch (Exception e) {
            log.error("获取或同步产品数据失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    /**
     * 检查TzProductSpu是否需要重新同步
     *
     * @param spu SPU实体
     * @return true-需要重新同步，false-数据仍然有效
     */
    private boolean needResync(TzProductSpu spu) {
        if (spu == null || spu.getGmtModified() == null) {
            return true;
        }

        // 检查数据是否超过3天未更新
        LocalDateTime lastModified = spu.getGmtModified();
        LocalDateTime now = LocalDateTime.now();
        Duration timeSinceUpdate = Duration.between(lastModified, now);

        boolean shouldResync = timeSinceUpdate.toDays() >= 3;

        if (shouldResync) {
            log.debug("TzProductSpu数据已过期，需要重新同步: spuId={}, 最后更新时间={}, 距今{}天", spu.getId(), lastModified, timeSinceUpdate
              .toDays());
        }

        return shouldResync;
    }

    @Override
    public AlibabaProductDetailDTO getAlibabaProductDetail(String platformProductId, boolean forceRefresh) {
        log.debug("获取阿里巴巴产品详情: platformProductId={}, forceRefresh={}", platformProductId, forceRefresh);

        try {
            Long productId = Long.valueOf(platformProductId);
            return pdcProductMappingRepository.getProductDetailWithCache(productId, forceRefresh);
        } catch (Exception e) {
            log.error("获取阿里巴巴产品详情失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    /**
     * 检查是否需要重新同步数据
     *
     * <pre>
     * 🔥 优化后的智能检测逻辑：
     * 1. 优先通过 metaInfoHash 检测数据变更（MD5签名对比）
     * 2. 兜底使用时间间隔检测（超过3天）
     * 3. 避免不必要的API调用，提升性能
     * </pre>
     *
     * @param platformProductId 平台产品ID
     * @return 是否需要重新同步
     */
    private boolean checkIfNeedResync(String platformProductId) {
        try {
            // 1. 首先获取现有的PdcProductMapping数据
            LambdaQueryWrapper<PdcProductMapping> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PdcProductMapping::getPlatformProductId, platformProductId)
              .eq(PdcProductMapping::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688);

            // 注意：这里需要注入PdcProductMappingMapper，暂时通过Repository获取
            // TODO: 后续可以直接注入Mapper提升性能

            // 2. 🔥 智能检测：通过metaInfoHash检测数据变更
            if (hasDataChangedByHash(platformProductId)) {
                log.debug("检测到数据变更（基于metaInfoHash），需要重新同步: platformProductId={}", platformProductId);
                return true;
            }

            // 3. 兜底机制：检查时间间隔
            if (hasDataExpiredByTime(platformProductId)) {
                log.debug("数据已过期（基于时间检测），需要重新同步: platformProductId={}", platformProductId);
                return true;
            }

            log.debug("数据无需重新同步: platformProductId={}", platformProductId);
            return false;

        } catch (Exception e) {
            log.warn("检查数据同步状态失败，默认不同步: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 🔥 通过metaInfoHash检测数据是否变更
     *
     * @param platformProductId 平台产品ID
     * @return true-数据已变更，false-数据未变更
     */
    private boolean hasDataChangedByHash(String platformProductId) {
        try {
            // 获取最新的商品详情数据
            // 强制刷新获取最新数据
            AlibabaProductDetailDTO latestProductDetail = pdcProductMappingRepository.getProductDetailWithCache(Long
              .valueOf(platformProductId), true);

            if (latestProductDetail == null) {
                log.debug("无法获取最新商品详情，跳过hash检测: {}", platformProductId);
                return false;
            }

            // 构建最新数据的metaInfo并计算MD5
            String latestHash = MetaInfoHashUtils.calculateMetaInfoHash(latestProductDetail);

            // 这里需要获取现有的metaInfoHash进行比较
            // 由于当前架构限制，暂时返回false，后续完善
            // TODO: 需要通过Repository获取现有的PdcProductMapping.metaInfoHash
            log.debug("计算得到最新hash: platformProductId={}, hash={}", platformProductId, latestHash);
            // 暂时返回false，后续完善
            return false;

        } catch (Exception e) {
            log.warn("metaInfoHash检测失败: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 检查数据是否基于时间过期
     *
     * @param platformProductId 平台产品ID
     * @return true-已过期，false-未过期
     */
    private boolean hasDataExpiredByTime(String platformProductId) {
        try {
            // 这里需要获取PdcProductMapping的gmt_modified字段
            // 由于当前架构限制，暂时返回false
            // TODO: 需要通过Repository获取PdcProductMapping实体并检查时间
            return false;

        } catch (Exception e) {
            log.warn("时间过期检测失败: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 降级数据源：从阿里巴巴产品详情构建基础的TzProductDTO
     *
     * @param platformProductId 平台产品ID
     * @return 基础的TzProductDTO
     */
    private TzProductDTO getProductFromFallbackSource(String platformProductId) {
        try {
            log.warn("尝试从降级数据源获取产品: platformProductId={}", platformProductId);

            AlibabaProductDetailDTO alibabaDetail = getAlibabaProductDetail(platformProductId, false);
            if (alibabaDetail == null) {
                log.error("降级数据源也无法找到产品: platformProductId={}", platformProductId);
                return null;
            }

            // 构建基础的TzProductDTO
            String mainImage = null;
            if (alibabaDetail.getImages() != null && !alibabaDetail.getImages().isEmpty()) {
                mainImage = alibabaDetail.getImages().get(0);
            } else if (alibabaDetail.getWhiteImage() != null) {
                mainImage = alibabaDetail.getWhiteImage();
            }

            TzProductDTO basicDTO = TzProductDTO.builder()
              .pdcPlatformProductId(platformProductId)
              .title(alibabaDetail.getTitle())
              .titleTrans(alibabaDetail.getTitleTrans())
              .mainImage(mainImage)
              .categoryId(alibabaDetail.getCategoryId())
              .categoryName(alibabaDetail.getCategoryName())
              .categoryNameTrans(alibabaDetail.getCategoryNameTrans())
              .description(alibabaDetail.getDescription())
              .unit(alibabaDetail.getUnit())
              .unitTrans(alibabaDetail.getUnitTrans())
              .whiteImage(alibabaDetail.getWhiteImage())
              .build();

            log.warn("降级数据源获取成功，返回基础产品信息: platformProductId={}", platformProductId);
            return basicDTO;

        } catch (Exception e) {
            log.error("降级数据源获取失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    // ==================== 简化同步逻辑的辅助方法 ====================

    /**
     * 从现有SPU构建TzProductDTO
     */
    private TzProductDTO buildTzProductDTOFromSpu(TzProductSpu spu, String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            List<TzProductSku> existingSkuList = getSkuListBySpuId(spu.getId());
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository.getProductDetailWithCache(productId,
              false);
            return TzProductMapping.INSTANCE.toTzProductDTO(spu, existingSkuList, productDetail);
        } catch (Exception e) {
            log.error("从SPU构建DTO失败: spuId={}, platformProductId={}", spu.getId(), platformProductId, e);
            return null;
        }
    }

    /**
     * 从产品详情同步产品数据
     */
    private TzProductDTO syncFromProductDetail(AlibabaProductDetailDTO productDetail) {
        try {
            if (productDetail == null) {
                log.warn("产品详情为空，无法同步");
                return null;
            }

            // 执行同步逻辑，复用现有的syncProductByPlatformId方法
            return syncProductByPlatformId(productDetail.getPlatformProductId());

        } catch (Exception e) {
            log.error("从产品详情同步失败: productId={}", productDetail != null ? productDetail.getId() : "null", e);
            return null;
        }
    }

    /**
     * 通过API调用获取并同步产品数据
     */
    private TzProductDTO syncFromApiCall(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            // 强制从API获取最新数据
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
              .getProductDetailWithCache(productId, true);

            if (productDetail == null) {
                log.warn("API调用获取产品详情失败: platformProductId={}", platformProductId);
                return null;
            }

            // 执行同步逻辑
            return syncFromProductDetail(productDetail);

        } catch (Exception e) {
            log.error("API调用同步失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    /**
     * 从产品详情同步到TzProduct表（重复方法，已合并到上面）
     */
    // 此方法已合并到syncFromProductDetail中，避免重复
}
