<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>fulfillmen-shop-openapi-service</artifactId>
    <name>Fulfillmen Shop OpenApi Service</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 依赖 manager 模块 不传递 -->
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-manager</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 依赖 dao 模块 不传递 -->
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-dao</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 依赖 domain 模块 -->
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-domain</artifactId>
        </dependency>
        <!-- 依赖 common 模块 -->
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-common</artifactId>
        </dependency>

    </dependencies>

</project>
