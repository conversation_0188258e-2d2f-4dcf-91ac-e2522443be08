/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.openapi.vo;

import com.fulfillmen.shop.openapi.convert.OpenapiCertificateListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * OpenAPI 产品详情VO
 *
 * <p>
 * 该VO会根据传入的language参数动态填充对应语言的内容：
 * <ul>
 * <li>language=zh：返回中文内容</li>
 * <li>language=en：返回英文内容，如果英文不存在则回退到中文</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/3 16:54
 * @description: 产品详情对外输出VO，支持多语言动态切换
 * @since 1.0.0
 */
@Data
@Schema(description = "产品详情信息")
public class OpenapiProductDetailVO {

    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "商品标题（根据language参数返回对应语言）")
    private String title;

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "类目名称（根据language参数返回对应语言）")
    private String categoryName;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品图片列表")
    private List<String> images;

    @Schema(description = "白底图")
    private String whiteImage;

    @Schema(description = "主视频")
    private String mainVideo;

    @Schema(description = "详情视频")
    private String detailVideo;

    @Schema(description = "商品单位（根据language参数返回对应语言）")
    private String unit;

    @Schema(description = "商品（人民币）价格")
    private BigDecimal price;

    @Schema(description = "商品（美元）价格")
    private BigDecimal usdPrice;

    @Schema(description = "卖家数据信息")
    private OpenapiSellerDataInfoVO sellerDataInfo;

    @Schema(description = "商品SKU列表")
    private List<OpenapiProductSkuInfoVO> productSkuInfos;

    @Schema(description = "商品属性列表")
    private List<OpenapiProductAttributeVO> productAttribute;

    @Schema(description = "商品销售信息")
    private OpenapiProductSaleInfoVO productSaleInfo;

    @Schema(description = "商品物流包裹信息")
    private OpenapiShippingInfoVO shippingInfo;

    @Schema(description = "商品证书列表")
    private OpenapiCertificateListVO certificateList;

    /**
     * 是否为单品
     * <pre>
     * 如果<code>true</code>单件商品，则 productSkuInfos is null
     * 如果<code>false</code>多SKU商品，则商品SKU不为空，价格从 productSkuInfos 中获取
     * </pre>
     */
    @Schema(description = "是否为单品。true：单件商品，价格从productSaleInfo获取；false：多SKU商品，价格从productSkuInfos获取")
    private Boolean isSingleItem;

    @Data
    @Schema(description = "商品SKU信息")
    public static class OpenapiProductSkuInfoVO {

        @Schema(description = "SKU ID")
        private Long skuId;

        @Schema(description = "规格ID")
        private String specId;

        @Schema(description = "SKU库存数量")
        private Integer amountOnSale;

        @Schema(description = "SKU价格（一件代发包邮价格）")
        private BigDecimal price;

        @Schema(description = "SKU（美元）价格")
        private BigDecimal usdPrice;

        @Schema(description = "SKU规格属性列表")
        private List<OpenapiProductSkuSpecVO> specs;
    }

    @Data
    @Schema(description = "商品属性")
    public static class OpenapiProductAttributeVO {

        @Schema(description = "属性ID")
        private String attributeId;

        @Schema(description = "属性名称（根据language参数返回对应语言）")
        private String attributeName;

        @Schema(description = "属性值（根据language参数返回对应语言）")
        private String value;
    }

    @Data
    @Schema(description = "商品销售信息")
    public static class OpenapiProductSaleInfoVO {

        @Schema(description = "商品库存")
        private Integer amountOnSale;

        @Schema(description = "商品（人民币）价格")
        private BigDecimal price;

        @Schema(description = "商品（美元）价格")
        private BigDecimal usdPrice;

        @Schema(description = "报价类型：0-无SKU按商品数量报价，1-按SKU规格报价，2-有SKU按商品数量报价")
        private Integer quoteType;

        @Schema(description = "商品单位（根据language参数返回对应语言）")
        private String unit;

        @Schema(description = "最小订购数量")
        private Integer minOrderQuantity;
    }
}
