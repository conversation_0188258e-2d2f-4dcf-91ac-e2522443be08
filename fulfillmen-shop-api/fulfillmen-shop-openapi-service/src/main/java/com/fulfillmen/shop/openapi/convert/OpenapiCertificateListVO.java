package com.fulfillmen.shop.openapi.convert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 证书列表VO
 *
 * <AUTHOR>
 * @date 2025/8/14
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenapiCertificateListVO {

    /**
     * 证书名字，如：外观专利证书或授权书证书
     */
    private String certificateName;

    /**
     * 证书编号
     */
    private String certificateCode;

    /**
     * 证书图片
     */
    private List<String> certificatePhotoList;
}
