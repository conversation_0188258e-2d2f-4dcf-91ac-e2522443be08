/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.openapi.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductSellerDataInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.*;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductShippingInfoDTO.AlibabaProductSkuShippingDetailDTO;
import com.fulfillmen.shop.openapi.enums.LanguageEnum;
import com.fulfillmen.shop.openapi.vo.OpenapiProductDetailVO;
import com.fulfillmen.shop.openapi.vo.OpenapiProductInfoVO;
import com.fulfillmen.shop.openapi.vo.OpenapiProductSkuSpecVO;
import com.fulfillmen.shop.openapi.vo.OpenapiSellerDataInfoVO;
import com.fulfillmen.shop.openapi.vo.OpenapiShippingInfoVO;
import com.fulfillmen.shop.openapi.vo.OpenapiShippingInfoVO.AlibabaProductSkuShippingDetailVO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * OpenAPI 产品转换映射器
 *
 * <AUTHOR>
 * @date 2025/6/3 17:06
 * @description: 支持多语言动态切换的产品数据转换器
 * @since 1.0.0
 */
@Mapper
public interface OpenapiProductConvertMapping {

    OpenapiProductConvertMapping INSTANCE = Mappers.getMapper(OpenapiProductConvertMapping.class);

    /**
     * 批量转换ProductInfoDTO列表为OpenapiProductInfoVO列表
     *
     * @param productInfoDTOList ProductInfoDTO列表
     * @param language           语言代码
     * @return OpenapiProductInfoVO列表
     */
    default List<OpenapiProductInfoVO> toOpenapiProductInfoVOList(List<ProductInfoDTO> productInfoDTOList,
        String language) {
        if (CollectionUtil.isEmpty(productInfoDTOList)) {
            return List.of();
        }

        LanguageEnum languageEnum = LanguageEnum.fromCode(language);
        return productInfoDTOList.stream()
            .map(dto -> toOpenapiProductInfoVO(dto, languageEnum))
            .collect(Collectors.toList());
    }

    /**
     * 将ProductInfoDTO转换为OpenapiProductInfoVO
     *
     * @param productInfoDTO ProductInfoDTO
     * @param language       语言枚举
     * @return OpenapiProductInfoVO
     */
    default OpenapiProductInfoVO toOpenapiProductInfoVO(ProductInfoDTO productInfoDTO, LanguageEnum language) {
        if (productInfoDTO == null) {
            return null;
        }

        OpenapiProductInfoVO vo = new OpenapiProductInfoVO();

        // 基本信息映射
        vo.setId(productInfoDTO.getId());
        vo.setImageUrl(productInfoDTO.getImageUrl());
        vo.setWhiteImageUrl(productInfoDTO.getWhiteImageUrl());
        vo.setMonthSold(productInfoDTO.getMonthSold());
        vo.setMinOrderQuantity(productInfoDTO.getMinOrderQuantity());
        vo.setRepurchaseRate(productInfoDTO.getRepurchaseRate());
        vo.setTopCategoryId(productInfoDTO.getTopCategoryId());
        vo.setSecondCategoryId(productInfoDTO.getSecondCategoryId());
        vo.setThirdCategoryId(productInfoDTO.getThirdCategoryId());
        vo.setIsOnePsale(productInfoDTO.getIsOnePsale());

        // 多语言字段处理
        vo.setTitle(getLocalizedText(productInfoDTO.getName(), productInfoDTO.getNameTrans(), language));

        // 价格处理
        if (Objects.nonNull(productInfoDTO.getPrice())) {
            try {
                BigDecimal price = productInfoDTO.getPrice();
                vo.setPrice(price);
                vo.setUsdPrice(productInfoDTO.getUsdPrice());
                // 格式化价格显示文本（可以根据需要添加货币符号等）
                // vo.setPriceText(formatPrice(price, language));
            } catch (NumberFormatException e) {
                // vo.setPriceText(productInfoDTO.getPrice());
            }
        }

        // 卖家信息转换
        if (productInfoDTO.getSellerDataInfo() != null) {
            vo.setSellerInfo(convertToOpenapiSellerDataInfo(productInfoDTO.getSellerDataInfo()));
        }

        // 可以根据分类ID设置分类名称（这里需要根据实际业务逻辑来实现）
        // vo.setCategoryName(getCategoryNameById(productInfoDTO.getTopCategoryId(), language));

        return vo;
    }

    /**
     * 格式化价格显示文本
     *
     * @param price    价格
     * @param language 语言枚举
     * @return 格式化后的价格文本
     */
    default String formatPrice(BigDecimal price, LanguageEnum language) {
        if (price == null) {
            return null;
        }

        if (language.isEnglish()) {
            return "$" + price.toString();
        } else {
            return "¥" + price.toString();
        }
    }

    /**
     * 转换为卖家基础信息VO
     */
    default OpenapiSellerDataInfoVO convertToOpenapiSellerDataInfo(TzProductSellerDataInfoDTO sellerDataInfo) {
        if (sellerDataInfo == null) {
            return null;
        }

        OpenapiSellerDataInfoVO sellerDataInfoVO = new OpenapiSellerDataInfoVO();

        sellerDataInfoVO.setTradeMedalLevel(sellerDataInfo.getTradeMedalLevel() != null
            ? Integer.valueOf(sellerDataInfo.getTradeMedalLevel())
            : null);
        sellerDataInfoVO.setCompositeServiceScore(sellerDataInfo.getCompositeServiceScore());
        sellerDataInfoVO.setRepeatPurchasePercent(sellerDataInfo.getRepeatPurchasePercent());
        sellerDataInfoVO.setLogisticsExperienceScore(sellerDataInfo.getLogisticsExperienceScore());
        sellerDataInfoVO.setDisputeComplaintScore(sellerDataInfo.getDisputeComplaintScore());
        sellerDataInfoVO.setOfferExperienceScore(sellerDataInfo.getOfferExperienceScore());
        sellerDataInfoVO.setAfterSalesExperienceScore(sellerDataInfo.getAfterSalesExperienceScore());
        sellerDataInfoVO.setConsultingExperienceScore(sellerDataInfo.getConsultingExperienceScore());

        return sellerDataInfoVO;
    }

    /**
     * 将阿里巴巴商品详情DTO转换为OpenAPI商品详情VO
     *
     * @param alibabaProductDetailDTO 阿里巴巴商品详情DTO
     * @param language                语言代码
     * @return OpenAPI商品详情VO
     */
    default OpenapiProductDetailVO toOpenapiProductDetailVO(AlibabaProductDetailDTO alibabaProductDetailDTO,
        String language) {
        if (alibabaProductDetailDTO == null) {
            return null;
        }

        LanguageEnum languageEnum = LanguageEnum.fromCode(language);
        OpenapiProductDetailVO vo = new OpenapiProductDetailVO();

        // 基本信息映射
        vo.setId(alibabaProductDetailDTO.getId());
        vo.setCategoryId(alibabaProductDetailDTO.getCategoryId());
        vo.setDescription(alibabaProductDetailDTO.getDescription());
        vo.setImages(alibabaProductDetailDTO.getImages());
        vo.setWhiteImage(alibabaProductDetailDTO.getWhiteImage());
        vo.setMainVideo(alibabaProductDetailDTO.getMainVideo());
        vo.setDetailVideo(alibabaProductDetailDTO.getDetailVideo());
        vo.setPrice(alibabaProductDetailDTO.getPrice());
        vo.setUsdPrice(alibabaProductDetailDTO.getUsdPrice());
        vo.setIsSingleItem(alibabaProductDetailDTO.isSingleItem());

        // 多语言字段处理
        vo.setTitle(getLocalizedText(alibabaProductDetailDTO.getTitle(), alibabaProductDetailDTO
            .getTitleTrans(), languageEnum));

        vo.setUnit(getLocalizedText(alibabaProductDetailDTO.getUnit(), alibabaProductDetailDTO
            .getUnitTrans(), languageEnum));

        // 类目名称处理（从AlibabaProductDetailDTO中获取）
        vo.setCategoryName(getLocalizedText(alibabaProductDetailDTO.getCategoryName(), alibabaProductDetailDTO
            .getCategoryNameTrans(), languageEnum));

        // 卖家信息转换
        if (alibabaProductDetailDTO.getSellerDataInfo() != null) {
            vo.setSellerDataInfo(convertToOpenapiSellerDataInfo(alibabaProductDetailDTO.getSellerDataInfo()));
        } else {
            // 如果没有卖家数据，创建空对象避免返回null
            vo.setSellerDataInfo(new OpenapiSellerDataInfoVO());
        }

        // SKU信息转换
        if (CollectionUtil.isNotEmpty(alibabaProductDetailDTO.getProductSkuList())) {
            List<OpenapiProductDetailVO.OpenapiProductSkuInfoVO> skuInfos = alibabaProductDetailDTO.getProductSkuList()
                .stream()
                .map(sku -> convertToOpenapiSkuInfo(sku, languageEnum))
                .collect(Collectors.toList());
            vo.setProductSkuInfos(skuInfos);
        }

        // 商品属性转换 CPV
        if (CollectionUtil.isNotEmpty(alibabaProductDetailDTO.getProductAttributeList())) {
            List<OpenapiProductDetailVO.OpenapiProductAttributeVO> attributes = alibabaProductDetailDTO
                .getProductAttributeList()
                .stream()
                .map(attr -> convertToOpenapiAttribute(attr, languageEnum))
                .collect(Collectors.toList());
            vo.setProductAttribute(attributes);
        }

        // 商品销售信息转换 ，如果 商品有 skuinfo 列表价格，这里不需要
        if (alibabaProductDetailDTO.getProductSaleInfo() != null && alibabaProductDetailDTO.isSingleItem()) {
            OpenapiProductDetailVO.OpenapiProductSaleInfoVO saleInfo = convertToOpenapiSaleInfo(alibabaProductDetailDTO
                .getProductSaleInfo(), languageEnum);
            vo.setProductSaleInfo(saleInfo);
        }

        // 商品物流包裹信息
        if (alibabaProductDetailDTO.getShippingInfo() != null) {
            vo.setShippingInfo(convertToOpenapiShippingInfo(alibabaProductDetailDTO.getShippingInfo()));
        }

        // 证书列表
        if (alibabaProductDetailDTO.getCertificateList() != null) {
            vo.setCertificateList(convertToOpenapiCertificateList((AlibabaProductCertificateListDTO) alibabaProductDetailDTO.getCertificateList()));
        }

        return vo;
    }

    /**
     * 根据语言枚举选择合适的文本
     *
     * @param chineseText 中文文本
     * @param englishText 英文文本
     * @param language    语言枚举
     * @return 本地化的文本，英文不存在时回退到中文
     */
    default String getLocalizedText(String chineseText, String englishText, LanguageEnum language) {
        if (language.isEnglish()) {
            // 英文优先，如果英文为空则回退到中文
            return StrUtil.isNotBlank(englishText) ? englishText : chineseText;
        }
        // 默认返回中文
        return chineseText;
    }

    /**
     * 转换SKU信息
     */
    default OpenapiProductDetailVO.OpenapiProductSkuInfoVO convertToOpenapiSkuInfo(AlibabaProductSkuDTO skuDTO,
        LanguageEnum language) {
        if (skuDTO == null) {
            return null;
        }

        OpenapiProductDetailVO.OpenapiProductSkuInfoVO skuInfo = new OpenapiProductDetailVO.OpenapiProductSkuInfoVO();

        skuInfo.setSkuId(skuDTO.getSkuId());
        skuInfo.setSpecId(skuDTO.getSpecId());
        skuInfo.setAmountOnSale(skuDTO.getAmountOnSale());
        skuInfo.setPrice(skuDTO.getPrice());
        skuInfo.setUsdPrice(skuDTO.getUsdPrice());

        // 转换SKU规格
        if (CollectionUtil.isNotEmpty(skuDTO.getSpecs())) {
            List<OpenapiProductSkuSpecVO> specs = skuDTO.getSpecs()
                .stream()
                .map(spec -> convertToOpenapiSkuSpec(spec, language))
                .collect(Collectors.toList());
            skuInfo.setSpecs(specs);
        }

        return skuInfo;
    }

    /**
     * 转换SKU规格
     */
    default OpenapiProductSkuSpecVO convertToOpenapiSkuSpec(AlibabaProductSkuSpecDTO specDTO, LanguageEnum language) {
        if (specDTO == null) {
            return null;
        }

        return OpenapiProductSkuSpecVO.builder()
            .attributeId(specDTO.getAttributeId())
            .attributeName(getLocalizedText(specDTO.getAttributeName(), specDTO.getAttributeNameTrans(), language))
            .value(getLocalizedText(specDTO.getValue(), specDTO.getValueTrans(), language))
            .skuImage(specDTO.getSkuImage())
            .build();
    }

    /**
     * 转换商品属性
     */
    default OpenapiProductDetailVO.OpenapiProductAttributeVO convertToOpenapiAttribute(AlibabaProductAttributeCPVDTO attributeDTO,
        LanguageEnum language) {
        if (attributeDTO == null) {
            return null;
        }

        OpenapiProductDetailVO.OpenapiProductAttributeVO attribute = new OpenapiProductDetailVO.OpenapiProductAttributeVO();

        attribute.setAttributeId(attributeDTO.getAttributeId());
        attribute.setAttributeName(getLocalizedText(attributeDTO.getAttributeName(), attributeDTO
            .getAttributeNameTrans(), language));
        attribute.setValue(getLocalizedText(attributeDTO.getValue(), attributeDTO.getValueTrans(), language));

        return attribute;
    }

    /**
     * 转换商品销售信息
     *
     * @param saleInfoDTO 阿里巴巴商品销售信息DTO
     * @param language    语言枚举
     * @return OpenapiProductDetailVO.OpenapiProductSaleInfoVO
     */
    default OpenapiProductDetailVO.OpenapiProductSaleInfoVO convertToOpenapiSaleInfo(com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSaleInfoDTO saleInfoDTO,
        LanguageEnum language) {
        if (saleInfoDTO == null) {
            return null;
        }

        OpenapiProductDetailVO.OpenapiProductSaleInfoVO saleInfo = new OpenapiProductDetailVO.OpenapiProductSaleInfoVO();

        saleInfo.setAmountOnSale(saleInfoDTO.getAmountOnSale());
        saleInfo.setQuoteType(saleInfoDTO.getQuoteType());
        saleInfo.setMinOrderQuantity(saleInfoDTO.getMinOrderQuantity());

        // 价格信息（优先使用一件代发价格）
        if (saleInfoDTO.getFenxiaoSaleInfo() != null && saleInfoDTO.getFenxiaoSaleInfo().getOnePiecePrice() != null) {
            saleInfo.setPrice(saleInfoDTO.getFenxiaoSaleInfo().getOnePiecePrice());
            saleInfo.setUsdPrice(saleInfoDTO.getFenxiaoSaleInfo().getUsdOnePiecePrice());
        } else if (CollectionUtil.isNotEmpty(saleInfoDTO.getPriceRangeList())) {
            // 使用价格区间的第一个价格
            saleInfo.setPrice(saleInfoDTO.getPriceRangeList().get(0).getPrice());
            saleInfo.setUsdPrice(saleInfoDTO.getPriceRangeList().get(0).getUsdPrice());
        }

        // 单位信息
        if (saleInfoDTO.getUnitInfo() != null) {
            saleInfo.setUnit(getLocalizedText(saleInfoDTO.getUnitInfo().getUnit(), saleInfoDTO.getUnitInfo()
                .getUnitTrans(), language));
        }

        return saleInfo;
    }

    /**
     * 转换商品物流包裹信息
     *
     * @param shippingInfoDTO 阿里巴巴商品物流包裹信息DTO
     * @return OpenapiShippingInfoVO
     */
    OpenapiShippingInfoVO convertToOpenapiShippingInfo(AlibabaProductShippingInfoDTO shippingInfoDTO);

    /**
     * sku 包裹信息
     *
     * @param skuShippingDetailDTO sku包裹信息
     * @return sku 包裹信息
     */
    AlibabaProductSkuShippingDetailVO convertToOpenapiSkuShippingDetail(AlibabaProductSkuShippingDetailDTO skuShippingDetailDTO);

    /**
     * 转换卖家信息
     *
     * @param sellerDataDTO 阿里巴巴商品卖家数据DTO
     * @return OpenapiSellerDataInfoVO
     */
    OpenapiSellerDataInfoVO convertToOpenapiSellerDataInfo(AlibabaProductSellerDataDTO sellerDataDTO);

    /**
     * 转换证书信息
     *
     * @param certificateListDTO 阿里巴巴商品证书列表DTO
     * @return OpenapiCertificateListVO
     */
    OpenapiCertificateListVO convertToOpenapiCertificateList(AlibabaProductCertificateListDTO certificateListDTO);
}
