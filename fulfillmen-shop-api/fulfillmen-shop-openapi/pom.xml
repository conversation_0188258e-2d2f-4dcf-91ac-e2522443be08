<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fulfillmen.shop</groupId>
    <artifactId>fulfillmen-shop</artifactId>
    <version>${revision}</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>
  <artifactId>fulfillmen-shop-openapi</artifactId>
  <name>Fulfillmen Shop OpenApi</name>
  <description>Fulfillmen Shop OpenApi 负责提供对外的接口</description>

  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.fulfillmen.shop</groupId>
      <artifactId>fulfillmen-shop-openapi-service</artifactId>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- mybatisPlus -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-data-mp</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- MySQL驱动 -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

</project>
