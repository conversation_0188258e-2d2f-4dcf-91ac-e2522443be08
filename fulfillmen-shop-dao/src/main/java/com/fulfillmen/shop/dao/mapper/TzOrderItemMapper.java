/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.dao.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.domain.dto.order.OrderDwdDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.starter.data.mp.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @date 2025/7/31 14:29
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface TzOrderItemMapper extends BaseMapper<TzOrderItem> {

    IPage<OrderDwdDTO> selectOrderDwdPage(Page<OrderDwdDTO> page,
      @Param("userId") Long userId,
      @Param("tenantId") Long tenantId,
      @Param("queryCondition") OrderDwdDTO queryCondition);
}
