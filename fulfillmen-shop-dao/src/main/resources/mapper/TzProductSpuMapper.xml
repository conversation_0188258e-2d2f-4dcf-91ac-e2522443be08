<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzProductSpuMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzProductSpu">
    <!--@mbg.generated-->
    <!--@Table tz_product_spu-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="title_trans" jdbcType="VARCHAR" property="titleTrans" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="description_trans" jdbcType="LONGVARCHAR" property="descriptionTrans" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_trans" jdbcType="VARCHAR" property="nameTrans" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_name_trans" jdbcType="VARCHAR" property="categoryNameTrans" />
    <result column="main_image" jdbcType="VARCHAR" property="mainImage" />
    <result column="white_image" jdbcType="VARCHAR" property="whiteImage" />
    <result column="images" jdbcType="VARCHAR" property="images" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_trans" jdbcType="VARCHAR" property="unitTrans" />
    <result column="main_video" jdbcType="VARCHAR" property="mainVideo" />
    <result column="detail_video" jdbcType="VARCHAR" property="detailVideo" />
    <result column="attribute_cpvs" jdbcType="VARCHAR" property="attributeCpvs" />
    <result column="shipping_info" jdbcType="VARCHAR" property="shippingInfo" />
    <result column="certificate_list" jdbcType="VARCHAR" property="certificateList" />
    <result column="sku_shipping_details" jdbcType="VARCHAR" property="skuShippingDetails" />
    <result column="min_order_quantity" jdbcType="INTEGER" property="minOrderQuantity" />
    <result column="is_pdc_sync" jdbcType="TINYINT" property="isPdcSync" />
    <result column="is_single_item" jdbcType="TINYINT" property="isSingleItem" />
    <result column="pdc_product_mapping_id" jdbcType="BIGINT" property="pdcProductMappingId" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="pdc_platform_product_id" jdbcType="VARCHAR" property="pdcPlatformProductId" />
    <result column="source_platform_seller_open_id" jdbcType="VARCHAR" property="sourcePlatformSellerOpenId" />
    <result column="source_platform_seller_name" jdbcType="VARCHAR" property="sourcePlatformSellerName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="putaway_time" jdbcType="TIMESTAMP" property="putawayTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, title, title_trans, description, description_trans, `name`, name_trans, category_id,
    category_name, category_name_trans, main_image, white_image, images, unit, unit_trans,
    main_video, detail_video, attribute_cpvs, shipping_info, certificate_list, sku_shipping_details, min_order_quantity,
    is_pdc_sync, is_single_item, pdc_product_mapping_id, platform_code, pdc_platform_product_id,
    source_platform_seller_open_id, source_platform_seller_name, `status`, putaway_time,
    tenant_id, is_deleted, revision, gmt_created, gmt_modified
  </sql>
</mapper>
