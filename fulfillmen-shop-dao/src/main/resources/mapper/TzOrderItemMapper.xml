<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzOrderItemMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzOrderItem">
    <!--@mbg.generated-->
    <!--@Table tz_order_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
    <result column="supplier_order_id" jdbcType="BIGINT" property="supplierOrderId" />
    <result column="platform_order_id" jdbcType="VARCHAR" property="platformOrderId" />
    <result column="line_number" jdbcType="INTEGER" property="lineNumber" />
    <result column="product_spu_id" jdbcType="BIGINT" property="productSpuId" />
    <result column="product_sku_id" jdbcType="BIGINT" property="productSkuId" />
    <result column="platform_product_id" jdbcType="VARCHAR" property="platformProductId" />
    <result column="platform_sku_id" jdbcType="VARCHAR" property="platformSkuId" />
    <result column="platform_spec_id" jdbcType="VARCHAR" property="platformSpecId" />
    <result column="platform_item_id" jdbcType="VARCHAR" property="platformItemId" />
    <result column="platform_snapshot_url" jdbcType="VARCHAR" property="platformSnapshotUrl" />
    <result column="platform_metadata" jdbcType="VARCHAR" property="platformMetadata" />
    <result column="product_title" jdbcType="VARCHAR" property="productTitle" />
    <result column="product_title_en" jdbcType="VARCHAR" property="productTitleEn" />
    <result column="product_link" jdbcType="VARCHAR" property="productLink" />
    <result column="sku_specs" jdbcType="VARCHAR" property="skuSpecs" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    <result column="product_image_url" jdbcType="VARCHAR" property="productImageUrl" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="actual_price" jdbcType="DECIMAL" property="actualPrice" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_en" jdbcType="VARCHAR" property="unitEn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="logistics_status" jdbcType="TINYINT" property="logisticsStatus" />
    <result column="error_code" jdbcType="INTEGER" property="errorCode" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="external_item_id" jdbcType="VARCHAR" property="externalItemId" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="weight_unit" jdbcType="VARCHAR" property="weightUnit" />
    <result column="completed_datetime" jdbcType="TIMESTAMP" property="completedDatetime" />
    <result column="is_single_item" jdbcType="TINYINT" property="isSingleItem" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

  <!-- 订单维度数据模型 -->
  <resultMap id="OrderDwdResultMap" type="com.fulfillmen.shop.domain.dto.order.OrderDwdDTO">
    <!-- 订单项 -->
    <association property="orderItem" javaType="com.fulfillmen.shop.domain.entity.TzOrderItem">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
      <result column="supplier_order_id" jdbcType="BIGINT" property="supplierOrderId" />
      <result column="platform_order_id" jdbcType="VARCHAR" property="platformOrderId" />
      <result column="line_number" jdbcType="INTEGER" property="lineNumber" />
      <result column="product_spu_id" jdbcType="BIGINT" property="productSpuId" />
      <result column="product_sku_id" jdbcType="BIGINT" property="productSkuId" />
      <result column="platform_product_id" jdbcType="VARCHAR" property="platformProductId" />
      <result column="platform_sku_id" jdbcType="VARCHAR" property="platformSkuId" />
      <result column="platform_spec_id" jdbcType="VARCHAR" property="platformSpecId" />
      <result column="platform_item_id" jdbcType="VARCHAR" property="platformItemId" />
      <result column="platform_snapshot_url" jdbcType="VARCHAR" property="platformSnapshotUrl" />
      <result column="platform_metadata" jdbcType="VARCHAR" property="platformMetadata" />
      <result column="product_title" jdbcType="VARCHAR" property="productTitle" />
      <result column="product_title_en" jdbcType="VARCHAR" property="productTitleEn" />
      <result column="product_link" jdbcType="VARCHAR" property="productLink" />
      <result column="sku_specs" jdbcType="VARCHAR" property="skuSpecs" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
      <result column="product_image_url" jdbcType="VARCHAR" property="productImageUrl" />
      <result column="price" jdbcType="DECIMAL" property="price" />
      <result column="actual_price" jdbcType="DECIMAL" property="actualPrice" />
      <result column="quantity" jdbcType="DECIMAL" property="quantity" />
      <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
      <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
      <result column="unit" jdbcType="VARCHAR" property="unit" />
      <result column="unit_en" jdbcType="VARCHAR" property="unitEn" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="logistics_status" jdbcType="TINYINT" property="logisticsStatus" />
      <result column="error_code" jdbcType="INTEGER" property="errorCode" />
      <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
      <result column="external_item_id" jdbcType="VARCHAR" property="externalItemId" />
      <result column="weight" jdbcType="VARCHAR" property="weight" />
      <result column="weight_unit" jdbcType="VARCHAR" property="weightUnit" />
      <result column="completed_datetime" jdbcType="TIMESTAMP" property="completedDatetime" />
      <result column="is_single_item" jdbcType="TINYINT" property="isSingleItem" />
      <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
      <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
      <result column="revision" jdbcType="INTEGER" property="revision" />
      <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
      <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </association>

    <!-- 采购订单 -->
    <association property="purchase" javaType="com.fulfillmen.shop.domain.entity.TzOrderPurchase">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
      <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
      <result column="buyer_type" jdbcType="TINYINT" property="buyerType" />
      <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
      <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
      <result column="paid_date" jdbcType="TIMESTAMP" property="paidDate" />
      <result column="paid_transaction_no" jdbcType="VARCHAR" property="paidTransactionNo" />
      <result column="order_completed_date" jdbcType="TIMESTAMP" property="orderCompletedDate" />
      <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
      <result column="exchange_rate_snapshot" jdbcType="DECIMAL" property="exchangeRateSnapshot" />
      <result column="customer_goods_amount" jdbcType="DECIMAL" property="customerGoodsAmount" />
      <result column="customer_total_freight" jdbcType="DECIMAL" property="customerTotalFreight" />
      <result column="customer_total_amount" jdbcType="DECIMAL" property="customerTotalAmount" />
      <result column="payable_coupon_amount" jdbcType="DECIMAL" property="payableCouponAmount" />
      <result column="payable_discount_amount" jdbcType="DECIMAL" property="payableDiscountAmount" />
      <result column="payable_plus_discount_amount" jdbcType="DECIMAL" property="payablePlusDiscountAmount" />
      <result column="payable_goods_amount" jdbcType="DECIMAL" property="payableGoodsAmount" />
      <result column="payable_freight_total" jdbcType="DECIMAL" property="payableFreightTotal" />
      <result column="payable_amount_total" jdbcType="DECIMAL" property="payableAmountTotal" />
      <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
      <result column="actual_payment_goods_amount" jdbcType="DECIMAL" property="actualPaymentGoodsAmount" />
      <result column="actual_payment_freight_amount" jdbcType="DECIMAL" property="actualPaymentFreightAmount" />
      <result column="actual_payment_discount_amount" jdbcType="DECIMAL" property="actualPaymentDiscountAmount" />
      <result column="actual_payment_coupon_amount" jdbcType="DECIMAL" property="actualPaymentCouponAmount" />
      <result column="actual_payment_plus_amount" jdbcType="DECIMAL" property="actualPaymentPlusAmount" />
      <result column="recipient_warehouse_id" jdbcType="BIGINT" property="recipientWarehouseId" />
      <result column="recipient_warehouse_name" jdbcType="VARCHAR" property="recipientWarehouseName" />
      <result column="delivery_address" jdbcType="LONGVARCHAR" property="deliveryAddress" />
      <result column="postal_code" jdbcType="VARCHAR" property="postalCode" />
      <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
      <result column="province" jdbcType="VARCHAR" property="province" />
      <result column="city" jdbcType="VARCHAR" property="city" />
      <result column="district" jdbcType="VARCHAR" property="district" />
      <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
      <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone" />
      <result column="supplier_count" jdbcType="INTEGER" property="supplierCount" />
      <result column="line_item_count" jdbcType="INTEGER" property="lineItemCount" />
      <result column="completed_supplier_count" jdbcType="INTEGER" property="completedSupplierCount" />
      <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity" />
      <result column="purchase_notes" jdbcType="LONGVARCHAR" property="purchaseNotes" />
      <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
      <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
      <result column="revision" jdbcType="INTEGER" property="revision" />
      <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
      <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </association>

    <!-- 供应商订单 -->
    <association property="supplier" javaType="com.fulfillmen.shop.domain.entity.TzOrderSupplier">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
      <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
      <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
      <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
      <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
      <result column="supplier_shop_name" jdbcType="VARCHAR" property="supplierShopName" />
      <result column="metadata_json" jdbcType="VARCHAR" property="metadataJson" />
      <result column="is_multiple_orders" jdbcType="TINYINT" property="isMultipleOrders" />
      <result column="external_sync_status" jdbcType="TINYINT" property="externalSyncStatus" />
      <result column="external_sync_failed_message" jdbcType="VARCHAR" property="externalSyncFailedMessage" />
      <result column="platform_order_id" jdbcType="VARCHAR" property="platformOrderId" />
      <result column="platform_order_no" jdbcType="VARCHAR" property="platformOrderNo" />
      <result column="platform_trade_no" jdbcType="VARCHAR" property="platformTradeNo" />
      <result column="platform_pay_url" jdbcType="VARCHAR" property="platformPayUrl" />
      <result column="platform_trade_type" jdbcType="VARCHAR" property="platformTradeType" />
      <result column="platform_trade_type_desc" jdbcType="VARCHAR" property="platformTradeTypeDesc" />
      <result column="platform_tracking_no" jdbcType="VARCHAR" property="platformTrackingNo" />
      <result column="wms_purchase_order_no" jdbcType="VARCHAR" property="wmsPurchaseOrderNo" />
      <result column="wms_sync_status" jdbcType="TINYINT" property="wmsSyncStatus" />
      <result column="wms_failed_message" jdbcType="VARCHAR" property="wmsFailedMessage" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
      <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
      <result column="procurement_date" jdbcType="TIMESTAMP" property="procurementDate" />
      <result column="shipped_date" jdbcType="TIMESTAMP" property="shippedDate" />
      <result column="delivered_date" jdbcType="TIMESTAMP" property="deliveredDate" />
      <result column="completed_date" jdbcType="TIMESTAMP" property="completedDate" />
      <result column="cancelled_date" jdbcType="TIMESTAMP" property="cancelledDate" />
      <result column="customer_goods_amount" jdbcType="DECIMAL" property="customerGoodsAmount" />
      <result column="customer_freight_amount" jdbcType="DECIMAL" property="customerFreightAmount" />
      <result column="customer_total_amount" jdbcType="DECIMAL" property="customerTotalAmount" />
      <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
      <result column="payable_goods_amount" jdbcType="DECIMAL" property="payableGoodsAmount" />
      <result column="payable_freight_amount" jdbcType="DECIMAL" property="payableFreightAmount" />
      <result column="payable_amount_total" jdbcType="DECIMAL" property="payableAmountTotal" />
      <result column="payable_discount_amount" jdbcType="DECIMAL" property="payableDiscountAmount" />
      <result column="payable_coupon_amount" jdbcType="DECIMAL" property="payableCouponAmount" />
      <result column="payable_plus_discount_amount" jdbcType="DECIMAL" property="payablePlusDiscountAmount" />
      <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
      <result column="actual_payment_goods_amount" jdbcType="DECIMAL" property="actualPaymentGoodsAmount" />
      <result column="actual_payment_freight_amount" jdbcType="DECIMAL" property="actualPaymentFreightAmount" />
      <result column="actual_payment_discount_amount" jdbcType="DECIMAL" property="actualPaymentDiscountAmount" />
      <result column="actual_payment_coupon_amount" jdbcType="DECIMAL" property="actualPaymentCouponAmount" />
      <result column="actual_payment_plus_amount" jdbcType="DECIMAL" property="actualPaymentPlusAmount" />
      <result column="line_item_count" jdbcType="INTEGER" property="lineItemCount" />
      <result column="completed_item_count" jdbcType="INTEGER" property="completedItemCount" />
      <result column="supplier_notes" jdbcType="VARCHAR" property="supplierNotes" />
      <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
      <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
      <result column="revision" jdbcType="INTEGER" property="revision" />
      <result column="created_by" jdbcType="BIGINT" property="createdBy" />
      <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
      <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </association>
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, purchase_order_id, supplier_order_id, platform_order_id, line_number, product_spu_id,
    product_sku_id, platform_product_id, platform_sku_id, platform_spec_id, platform_item_id,
    platform_snapshot_url, platform_metadata, product_title, product_title_en, product_link,
    sku_specs, product_image_url, price, actual_price, quantity, total_amount, actual_payment_amount,
    unit, unit_en, `status`, logistics_status, error_code, error_message, external_item_id,
    weight, weight_unit, completed_datetime, is_single_item, tenant_id, is_deleted, revision,
    gmt_created, gmt_modified
  </sql>

  <select id="selectOrderDwdPage" resultMap="OrderDwdResultMap">
    with order_dwd as (
    select
    <!-- 订单项 -->
    i.id,
    i.purchase_order_id,
    i.product_spu_id,
    i.product_sku_id,
    i.platform_product_id,
    i.platform_sku_id,
    i.platform_spec_id,
    i.platform_item_id,
    i.product_title,
    i.product_title_en,
    i.sku_specs,
    i.product_image_url,
    i.price,
    i.quantity,
    i.total_amount,
    i.actual_payment_amount,
    i.unit,
    i.unit_en,
    i.`status`,
    i.logistics_status,
    i.weight,
    i.weight_unit,
    i.completed_datetime,
    i.is_single_item,
    i.tenant_id,
    i.is_deleted,
    i.gmt_created,
    i.gmt_modified,
    <!-- 采购订单 -->
    p.id as purchase_id,
    p.purchase_order_no,
    p.buyer_id,
    p.buyer_type,
    p.order_status,
    p.order_date,
    p.paid_date,
    p.order_completed_date,
    p.total_quantity,
    p.line_item_count,
    p.paid_transaction_no,
    p.customer_goods_amount,
    p.customer_total_freight,
    p.customer_total_amount,
    p.exchange_rate_snapshot,
    p.recipient_warehouse_id,
    p.recipient_warehouse_name,
    p.consignee_name,
    p.consignee_phone,
    p.purchase_notes,
    <!-- 供应商订单 -->
    s.id as supplier_order_id,
    s.supplier_order_no,
    s.supplier_id,
    s.supplier_name,
    s.supplier_shop_name,
    s.platform_order_id,
    s.platform_order_no,
    s.platform_trade_no,
    s.platform_pay_url,
    s.platform_trade_type,
    s.platform_tracking_no,
    s.wms_purchase_order_no
    from
    tz_order_item i
    left join tz_order_purchase p on i.purchase_order_id = p.id and p.is_deleted = 0
    left join tz_order_supplier s on i.supplier_order_id = s.id and s.is_deleted = 0
    where
    i.tenant_id = #{tenantId}
    and p.buyer_id = #{userId}
    and i.is_deleted = 0
    <if test="queryCondition != null">
      <if test="queryCondition.status != null">
        and p.order_status = #{queryCondition.status}
      </if>
      <if test="queryCondition.startTime != null and queryCondition.endTime != null">
        and i.gmt_created between #{queryCondition.startTime} and #{queryCondition.endTime}
      </if>
      <if test="queryCondition.keyword != null and queryCondition.keyword != ''">
        and (
        p.purchase_order_no like concat('%', #{queryCondition.keyword}, '%')
        or i.product_title like concat('%', #{queryCondition.keyword}, '%')
        or i.product_title_en like concat('%', #{queryCondition.keyword}, '%')
        )
      </if>
      <if test="queryCondition.skuId != null and queryCondition.skuId != ''">
        and i.product_sku_id = #{queryCondition.skuId}
      </if>
    </if>
    )
    select * from order_dwd
    group by purchase_order_id
    order by purchase_order_id desc
  </select>
</mapper>
