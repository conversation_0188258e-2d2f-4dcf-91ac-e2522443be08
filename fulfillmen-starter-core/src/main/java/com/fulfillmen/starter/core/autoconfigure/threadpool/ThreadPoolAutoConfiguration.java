/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.core.autoconfigure.threadpool;

import com.alibaba.ttl.TtlRunnable;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.task.ThreadPoolTaskExecutorCustomizer;
import org.springframework.boot.task.ThreadPoolTaskSchedulerCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 线程池配置扩展 扩展 Spring Boot 的 TaskExecutionAutoConfiguration，优化线程池配置
 *
 * <AUTHOR>
 * @date 2024年12月13日17:45:28
 * @since 1.0.0
 */
@EnableAsync
@Configuration(proxyBeanMethods = false)
@AutoConfiguration(before = TaskExecutionAutoConfiguration.class)
@EnableConfigurationProperties(ThreadPoolExtensionProperties.class)
public class ThreadPoolAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(ThreadPoolAutoConfiguration.class);

    /**
     * 自定义异步任务线程池配置器
     * <pre>
     * 提供 ThreadPoolTaskExecutorCustomizer，用于自定义线程池配置
     * </pre>
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    @ConditionalOnProperty(prefix = "spring.task.execution", name = "enabled", havingValue = "true", matchIfMissing = true)
    public ThreadPoolTaskExecutorCustomizer threadPoolTaskExecutorCustomizer(ThreadPoolExtensionProperties properties) {
        return executor -> {
            if (Boolean.TRUE.equals(properties.getExecution().getEnabled())) {
                // 设置核心线程数
                executor.setCorePoolSize(properties.getExecution().calculateCoreSize());
                // 设置最大线程数
                executor.setMaxPoolSize(properties.getExecution().calculateMaxSize());
                // 设置队列容量
                executor.setQueueCapacity(properties.getExecution().getQueueCapacity());
                // 设置线程名前缀
                executor.setThreadNamePrefix(properties.getExecution().getThreadNamePrefix());
                // 设置拒绝策略
                executor.setRejectedExecutionHandler(properties.getExecution()
                    .getRejectedPolicy()
                    .getRejectedExecutionHandler());
                // 设置TTL包装任务
                executor.setTaskDecorator(TtlRunnable::get);
                log.info("[Fulfillmen Starter] - ThreadPool extension configuration applied: " + "coreSize={}, maxSize={}, queueCapacity={}, threadNamePrefix={}, rejectedPolicy={}", properties
                    .getExecution()
                    .calculateCoreSize(), properties.getExecution().calculateMaxSize(), properties.getExecution()
                        .getQueueCapacity(), properties.getExecution().getThreadNamePrefix(), properties.getExecution()
                            .getRejectedPolicy());
            }
        };
    }

    /**
     * 自定义调度任务线程池配置
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    @ConditionalOnProperty(prefix = "spring.task.scheduling", name = "enabled", havingValue = "true", matchIfMissing = true)
    public ThreadPoolTaskSchedulerCustomizer threadPoolTaskSchedulerCustomizer(ThreadPoolExtensionProperties properties) {
        return scheduler -> {
            if (Boolean.TRUE.equals(properties.getScheduling().getEnabled())) {
                // 设置线程池大小
                scheduler.setPoolSize(properties.getScheduling().getPoolSize());
                // 设置线程名前缀
                scheduler.setThreadNamePrefix(properties.getScheduling().getThreadNamePrefix());
                // 设置拒绝策略
                scheduler.setRejectedExecutionHandler(properties.getScheduling()
                    .getRejectedPolicy()
                    .getRejectedExecutionHandler());

                log.info("[Fulfillmen Starter] - TaskScheduler extension configuration applied: " + "poolSize={}, threadNamePrefix={}, rejectedPolicy={}", properties
                    .getScheduling()
                    .getPoolSize(), properties.getScheduling().getThreadNamePrefix(), properties.getScheduling()
                        .getRejectedPolicy());
            }
        };
    }

    @PostConstruct
    public void postConstruct() {
        log.debug("[Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.");
    }
}
