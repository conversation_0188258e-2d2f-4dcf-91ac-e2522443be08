/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.enums;

/**
 * Fulfillmen 业务错误码枚举
 *
 * <pre>
 * 取值范围：1000-1999 (业务相关错误)
 * 1000-1099 认证授权错误（包括登录失败、权限不足、token错误等）
 * 1100-1199 系统错误（包括内部错误、数据库错误、配置错误等）
 * 1200-1299 外部服务错误（包括网络错误、第三方服务错误、超时等）
 * 1300-1399 签名验证错误（包括签名校验、时间戳等）
 * 1400-1499 加密解密错误
 * 1500-1599 分类管理错误
 * 1600-1699 产品管理错误
 * 1700-1799 购物车业务错误
 * 1800-1899 数据库操作错误
 * 1900-1999 业务异常处理错误
 *
 * 注意：参数校验相关错误请使用 FulfillmenValidationCodeEnum (错误码范围 3000-3800)
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/10
 * @description: 业务错误码枚举
 * @since 1.0.0
 */
public enum FulfillmenErrorCodeEnum implements IErrorResult {

    // ==================== 认证授权错误 1000-1099 ====================
    /**
     * 未登录
     */
    NOT_LOGIN(1000, "Not logged in", "global.auth.not.login"),

    /**
     * 登录失败
     */
    LOGIN_FAILED(1001, "Login failed", "global.auth.login.failed"),

    /**
     * 用户不存在
     */
    USER_NOT_FOUND(1002, "User not found", "global.auth.user.not.found"),

    /**
     * 密码错误
     */
    PASSWORD_ERROR(1003, "Password error", "global.auth.password.error"),

    /**
     * 权限不足
     */
    PERMISSION_DENIED(1004, "Permission denied for {0}", "global.auth.permission.denied") {

        @Override
        public String[] getParameterDescriptions() {
            return new String[]{"resource or operation"};
        }
    },

    /**
     * token无效
     */
    TOKEN_INVALID(1005, "Token invalid", "global.auth.token.invalid"),

    /**
     * token过期
     */
    TOKEN_EXPIRED(1006, "Token expired", "global.auth.token.expired"),

    /**
     * 账户被禁用
     */
    ACCOUNT_DISABLED(1007, "Account disabled", "global.auth.account.disabled"),

    /**
     * 账户被锁定
     */
    ACCOUNT_LOCKED(1008, "Account locked", "global.auth.account.locked"),

    /**
     * 您已被踢下线
     */
    KICK_OUT(1009, "You have been kicked offline", "global.auth.kick.out"),

    /**
     * 您已被顶下线
     */
    BE_REPLACED_MESSAGE(1010, "You have been replaced", "global.auth.be.replaced.message"),

    // ==================== 系统错误 1100-1199 ====================
    /**
     * 系统内部错误
     */
    SYSTEM_ERROR(1100, "System internal error", "global.system.error"),

    /**
     * 数据库连接错误
     */
    DATABASE_CONNECTION_ERROR(1101, "Database connection error", "global.database.connection.error"),

    /**
     * 数据库操作失败
     */
    DATABASE_OPERATION_ERROR(1102, "Database operation failed", "global.database.operation.error"),

    /**
     * 缓存操作失败
     */
    CACHE_OPERATION_ERROR(1103, "Cache operation failed", "global.cache.operation.error"),

    /**
     * 配置错误
     */
    CONFIG_ERROR(1104, "Configuration error", "global.config.error"),

    /**
     * 资源不存在
     */
    RESOURCE_NOT_FOUND(1105, "Resource not found", "global.resource.not.found"),

    /**
     * 资源已存在
     */
    RESOURCE_ALREADY_EXISTS(1106, "Resource already exists", "global.resource.already.exists"),

    /**
     * 操作频繁
     */
    OPERATION_TOO_FREQUENT(1107, "Operation too frequent", "global.operation.too.frequent"),

    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(1108, "Service unavailable", "global.service.unavailable"),

    /**
     * 并发冲突
     */
    CONCURRENT_CONFLICT(1109, "Concurrent conflict", "global.concurrent.conflict"),

    /**
     * 操作错误
     */
    OPERATION_FAILED(1110, "Operation is failed", "global.operation.failed"),

    /**
     * wms账户无法检索
     */
    UNABLE_RETRIEVE_WMS_ACCOUNT(1111, "Unable to retrieve WMS account information", "global.unable.retrieve.wms.account"),

    /**
     * 未找到相关信息
     */
    RELEVANT_INFORMATION_NOT_FOUND(1112, "No relevant information found", "global.no.relevant.information.found"),

    // ==================== 外部服务错误 1200-1299 ====================
    /**
     * 网络连接错误
     */
    NETWORK_ERROR(1200, "Network connection error", "global.network.error"),

    /**
     * 网络超时
     */
    NETWORK_TIMEOUT(1201, "Network timeout", "global.network.timeout"),

    /**
     * 第三方服务错误
     */
    THIRD_PARTY_SERVICE_ERROR(1202, "Third party service error", "global.third.party.service.error"),

    /**
     * 第三方服务不可用
     */
    THIRD_PARTY_SERVICE_UNAVAILABLE(1203, "Third party service unavailable", "global.third.party.service.unavailable"),

    /**
     * API调用失败
     */
    API_CALL_FAILED(1204, "API call to {0} failed: {1}", "global.api.call.failed"),

    /**
     * 响应解析错误
     */
    RESPONSE_PARSE_ERROR(1205, "Response parse error", "global.response.parse.error"),

    /**
     * 远程服务返回错误
     */
    REMOTE_SERVICE_ERROR(1206, "Remote service returned error", "global.remote.service.error"),

    /**
     * 限流错误
     */
    RATE_LIMIT_ERROR(1207, "Rate limit exceeded", "global.rate.limit.error"),

    /**
     * 熔断器开启
     */
    CIRCUIT_BREAKER_OPEN(1208, "Circuit breaker is open", "global.circuit.breaker.open"),

    /**
     * 服务降级
     */
    SERVICE_DEGRADED(1209, "Service degraded", "global.service.degraded"),

    // ==================== 签名验证错误 1300-1399 ====================
    /**
     * 签名参数缺失
     */
    SIGNATURE_PARAM_MISSING(1300, "Missing signature parameter", "global.signature.param.missing"),

    /**
     * 签名验证失败
     */
    SIGNATURE_INVALID(1301, "Invalid signature", "global.signature.invalid"),

    /**
     * 签名验证异常
     */
    SIGNATURE_VALIDATION_ERROR(1302, "Signature validation error", "global.signature.validation.error"),

    /**
     * 签名必需参数缺失
     */
    SIGNATURE_REQUIRED_PARAMS_MISSING(1303, "Missing required signature parameters", "global.signature.required.params.missing"),

    // ==================== 加密解密错误 1400-1499 ====================
    /**
     * 字段加密异常
     */
    FIELD_ENCRYPTION_ERROR(1400, "Field encryption error", "global.field.encryption.error"),

    /**
     * 字段解密异常
     */
    FIELD_DECRYPT_ERROR(1401, "Field decrypt error", "global.field.decrypt.error"),

    // ==================== 分类管理错误 1500-1599 ====================
    /**
     * 获取分类失败
     */
    CATEGORY_GET_FAILED(1500, "Failed to get category", "global.category.get.failed"),

    /**
     * 获取分类翻译失败
     */
    CATEGORY_TRANSLATION_GET_FAILED(1501, "Failed to get category translation", "global.category.translation.get.failed"),

    /**
     * 获取分类属性失败
     */
    CATEGORY_ATTRIBUTES_GET_FAILED(1502, "Failed to get category attributes", "global.category.attributes.get.failed"),

    // ==================== 产品管理错误 1600-1699 ====================
    /**
     * 获取商品详情失败
     */
    PRODUCT_DETAIL_GET_FAILED(1600, "Failed to get product detail", "global.product.detail.get.failed"),

    /**
     * 商品搜索失败
     */
    PRODUCT_SEARCH_FAILED(1601, "Failed to search products", "global.product.search.failed"),

    /**
     * 文件处理失败
     */
    FILE_PROCESS_FAILED(1602, "Failed to process file", "global.file.process.failed"),

    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAILED(1603, "Failed to upload file", "global.file.upload.failed"),

    /**
     * 图片搜索失败
     */
    IMAGE_SEARCH_FAILED(1604, "Failed to search by image", "global.image.search.failed"),

    /**
     * 获取卖家商品失败
     */
    SELLER_PRODUCTS_GET_FAILED(1605, "Failed to get seller products", "global.seller.products.get.failed"),

    /**
     * 获取关键词导航失败
     */
    KEYWORD_NAVIGATION_GET_FAILED(1606, "Failed to get keyword navigation", "global.keyword.navigation.get.failed"),

    /**
     * 图片上传失败
     */
    IMAGE_UPLOAD_FAILED(1607, "Failed to upload image", "global.image.upload.failed"),

    /**
     * 图片处理失败
     */
    IMAGE_PROCESS_FAILED(1608, "Failed to process image", "global.image.process.failed"),

    /**
     * 获取推荐商品失败
     */
    RECOMMEND_PRODUCTS_GET_FAILED(1609, "Failed to get recommend products", "global.recommend.products.get.failed"),

    /**
     * 获取相关推荐商品失败
     */
    RELATED_RECOMMEND_PRODUCTS_GET_FAILED(1610, "Failed to get related recommend products", "global.related.recommend.products.get.failed"),

    /**
     * 商品同步失败
     */
    PRODUCT_SYNC_FAILED(1611, "Product synchronization failed: {0}", "global.product.sync.failed") {

        @Override
        public String[] getParameterDescriptions() {
            return new String[]{"product ID"};
        }
    },

    /**
     * 产品同步超时
     */
    PRODUCT_SYNC_TIMEOUT(1612, "Product synchronization timeout: {0}", "global.product.sync.timeout") {

        @Override
        public String[] getParameterDescriptions() {
            return new String[]{"product ID"};
        }
    },

    /**
     * 产品详情服务降级
     */
    PRODUCT_DETAIL_SERVICE_DEGRADED(1613, "Product detail service degraded, using fallback: {0}", "global.product.detail.service.degraded") {

        @Override
        public String[] getParameterDescriptions() {
            return new String[]{"product ID"};
        }
    },

    // ==================== 购物车业务错误 1700-1799 ====================
    /**
     * 购物车删除失败
     */
    SHOPPING_CART_DELETE_FAILED(1700, "Shopping cart delete failed: {0}", "global.shopping.cart.delete.failed") {

        @Override
        public String[] getParameterDescriptions() {
            return new String[]{"product ID"};
        }
    },

    /**
     * 购物车清空失败
     */
    SHOPPING_CART_CLEAR_FAILED(1701, "Shopping cart clear failed", "global.shopping.cart.clear.failed"),

    /**
     * 购物车更新失败
     */
    SHOPPING_CART_UPDATE_FAILED(1702, "Shopping cart update failed: {0}", "global.shopping.cart.update.failed") {

        @Override
        public String[] getParameterDescriptions() {
            return new String[]{"product ID or SKU ID"};
        }
    },

    // ==================== 数据库操作错误 1800-1899 ====================
    /**
     * 批量插入失败
     */
    BATCH_INSERT_FAILED(1800, "Batch insert operation failed", "global.batch.insert.failed"),

    /**
     * 批量更新失败
     */
    BATCH_UPDATE_FAILED(1801, "Batch update operation failed", "global.batch.update.failed"),

    /**
     * 获取阿里巴巴分类失败
     */
    ALIBABA_CATEGORIES_GET_FAILED(1802, "Failed to get alibaba categories", "global.alibaba.categories.get.failed"),

    /**
     * 数据处理失败
     */
    DATA_PROCESSING_FAILED(1803, "Data processing failed, please try again later", "global.data.processing.failed"),

    /**
     * 数据库操作失败
     */
    DATABASE_OPERATION_FAILED(1804, "Database operation failed, please try again later", "global.database.operation.failed"),

    /**
     * 数据访问失败
     */
    DATA_ACCESS_FAILED(1805, "Data access failed, please try again later", "global.data.access.failed"),

    // ==================== 通用的错误 1900-1999 ====================

    /**
     * 请求方法不支持
     */
    COMMON_METHOD_NOT_SUPPORTED(1900, "Request method {0} not supported", "global.method.not.supported"),
    /**
     * 订单预览令牌过期
     */
    ORDER_PREVIEW_TOKEN_EXPIRED(1901, "Order preview token expired", "global.order.preview.token.expired"),

    /**
     * 订单预览令牌不存在
     */
    ORDER_PREVIEW_TOKEN_NOT_FOUND(1902, "Order preview token not found", "global.order.preview.token.not.found"),

    /**
     * 订单预览令牌数据异常
     */
    ORDER_PREVIEW_TOKEN_DATA_ERROR(1903, "Order preview token data error", "global.order.preview.token.data.error"),

    /**
     * 系统内部错误
     */
    INTERNAL_SYSTEM_ERROR(-1, "Internal system error, please contact administrator", "global.internal.system.error"),
    /**
     * 租户仓库不存在
     */
    TENANT_WAREHOUSE_NOT_FOUND(1904, "Tenant warehouse not found", "global.tenant.warehouse.not.found"),

    /**
     * 订单不能取消
     */
    ORDER_CANCEL_FAILED(1905, "Order cancelled failed", "global.order.cannot.cancel"),

    /**
     * 支付失败，余额不足。
     */
    ORDER_PAYMENT_FAILED(1906, "Payment failed, insufficient balance", "global.order.pay.failed"),
    /**
     * 订单支付失败，请稍后重试
     */
    ORDER_PAYMENT_FAILED_UNKNOWN(1906, "Order payment failed, please try again later.", "global.order.pay.failed.unknown"),
    /**
     * 订单提交失败，请稍后重试
     */
    ORDER_SUBMIT_FAILED(1907, "Order submission failed, please try again later.", "global.order.submit.failed"),

    /**
     * 预估运费失败
     */
    FREIGHT_ESTIMATE_FAILED(1908, "Freight estimate failed.", "global.freight.estimate.failed"),
    ;

    private final Integer code;
    private final String message;
    private final String i18nKey;

    FulfillmenErrorCodeEnum(Integer code, String message, String i18nKey) {
        this.code = code;
        this.message = message;
        this.i18nKey = i18nKey;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getI18nKey() {
        return i18nKey;
    }
}
