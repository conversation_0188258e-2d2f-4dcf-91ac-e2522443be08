0000000000000000000000000000000000000000 0b5ab0d150b86c34830c5c41b475b09775e8b2b9 james <<EMAIL>> 1747992173 +0800	branch: Created from origin/develop
e1649f2c3dfc5e6c803b669ce39246833bd49a28 13dc7df1202573a9b2393ebcabe1f2958aeb79ae james <<EMAIL>> ********** +0800	commit: 移除 openapi 自定义异常。整合进全局异常处理类
13dc7df1202573a9b2393ebcabe1f2958aeb79ae 46855960435d88a6afa9bbb4d4d1ef0431afb46c james <<EMAIL>> ********** +0800	commit: 新增阿里巴巴Ocean客户端依赖配置文档及安装脚本
46855960435d88a6afa9bbb4d4d1ef0431afb46c ee6b00f2c9e79d0a8566bb80c0354e0884ba926a james <<EMAIL>> ********** +0800	commit: perf(openapi): 优化 nonce防重放攻击验证逻辑- 修改 validateNonceForReplayAttack 方法，增加 accountInfo 参数
ee6b00f2c9e79d0a8566bb80c0354e0884ba926a 1d3e9224fd1b0c54a1ccb6a5847bc32c1178eea5 james <<EMAIL>> ********** +0800	commit: feat(fulfillmen-shop): 新增货币转换服务及相关文档
1d3e9224fd1b0c54a1ccb6a5847bc32c1178eea5 469f1223251624f916fe96b14327b7e70bacfda6 james <<EMAIL>> ********** +0800	commit: feat(汇率): 实现汇率缓存管理和货币转换功能
469f1223251624f916fe96b14327b7e70bacfda6 1051edb107b5b175fd95bbf0d539b3004f645542 james <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
b532f6ceb28b3e1d71a0a824205d80febe562b04 b602b61786f655a2bf37b986bd21318ef498c8c5 james <<EMAIL>> ********** +0800	rebase (finish): refs/heads/develop onto 1051edb107b5b175fd95bbf0d539b3004f645542
b602b61786f655a2bf37b986bd21318ef498c8c5 5167ffa425f7d8204cf72a171dd4bb2d54f527f4 james <<EMAIL>> ********** +0800	commit: feat(文档): 新增 Fulfillmen Shop 快速开发指南及优化总结
5167ffa425f7d8204cf72a171dd4bb2d54f527f4 56bd2c68ba93a84b9db6def6eccf2d73f357cfcc james <<EMAIL>> 1750143999 +0800	commit: feat(配置): 更新数据源和国际化支持
56bd2c68ba93a84b9db6def6eccf2d73f357cfcc 83c1e09b33fabcba31281ac101328996d5dfc253 james <<EMAIL>> 1750153737 +0800	commit: fix(fulfillmen-shop-common):修复 URL 编码在签名验证中的问题
83c1e09b33fabcba31281ac101328996d5dfc253 7bace26df4606c99abf9a7f5341d07524fa3326a james <<EMAIL>> 1750157512 +0800	commit: feat(多租户): 新增多租户相关功能及文档
7bace26df4606c99abf9a7f5341d07524fa3326a d1dd7eaf027bfd3f7bb4e9f99a0c1507a3a71192 james <<EMAIL>> 1750241320 +0800	commit: refactor(domain): 重构产品信息相关 DTO 类
d1dd7eaf027bfd3f7bb4e9f99a0c1507a3a71192 b57eb46545646332ca32d835eb7909677abecdae james <<EMAIL>> 1750391434 +0800	commit: rm build bug
b57eb46545646332ca32d835eb7909677abecdae db3471e1b770b8abb8590b231a00415040a16214 james <<EMAIL>> 1750391440 +0800	merge origin/develop: Merge made by the 'ort' strategy.
db3471e1b770b8abb8590b231a00415040a16214 9a558bd52d8372711746c7763325fc10d08a4738 james <<EMAIL>> 1750406313 +0800	commit: refactor(fulfillmen-shop):优化代码格式和结构
9a558bd52d8372711746c7763325fc10d08a4738 72dbb09034c76eab87e9bf3cbf362f7da286ab9b james <<EMAIL>> 1750408949 +0800	commit: refactor(fulfillmen-shop):优化 API 签名脚本和用户上下文类
72dbb09034c76eab87e9bf3cbf362f7da286ab9b 5549f5e3167505d952ecad77341483f0ec8be35c james <<EMAIL>> 1750408975 +0800	rebase (finish): refs/heads/develop onto db3471e1b770b8abb8590b231a00415040a16214
5549f5e3167505d952ecad77341483f0ec8be35c 4c372ffe0fadd0231dd6684ef37930fa4890eef4 james <<EMAIL>> 1750413174 +0800	commit: feat(kubernetes): 优化 fulfillmen-shop 部署配置
4c372ffe0fadd0231dd6684ef37930fa4890eef4 961810ddea32dff09f581a873b70723ac5285a70 james <<EMAIL>> 1750414083 +0800	commit: feat(kubernetes): 优化 fulfillmen-shop 部署配置
961810ddea32dff09f581a873b70723ac5285a70 be02b537ecd02e4db3b852cdd7902d0e39d52c96 james <<EMAIL>> 1750414128 +0800	commit (amend): fixed managed 安全 bug
be02b537ecd02e4db3b852cdd7902d0e39d52c96 b92b97ef96619f21ebcec8b414f3f0d26b6ce63c james <<EMAIL>> 1750414744 +0800	commit: 更新 application-sealos.yml 文件，新增允许跨域的域名配置，包括 'shop.nayasource.com'、'shop.fulfillmen.com' 和 'shop.1688hub.com'，以支持更多的跨域请求来源。
b92b97ef96619f21ebcec8b414f3f0d26b6ce63c 63ee941d649377c4e5a55b799d5adb81981f87bb james <<EMAIL>> 1750474613 +0800	commit: docs(shop): 更新跨域配置并添加项目描述
63ee941d649377c4e5a55b799d5adb81981f87bb cfdb246f6773e76aceb0a5028c945e6c276fb13e james <<EMAIL>> 1750728281 +0800	commit: build(bootstrap): 引入 MyBatis Plus 配置
cfdb246f6773e76aceb0a5028c945e6c276fb13e 1d71084aa4e4e7b3cf8cf978aa6a0fffeea9ceb6 james <<EMAIL>> 1750748608 +0800	commit: feat(限流): 优化产品接口的限流策略与性能
1d71084aa4e4e7b3cf8cf978aa6a0fffeea9ceb6 48e7c91b1a644935d481a181f76c1fe031ec69cf james <<EMAIL>> 1750821272 +0800	merge origin/develop: Fast-forward
48e7c91b1a644935d481a181f76c1fe031ec69cf 615ad5a904e04edf41311ab91e2ab7c4712e1767 james <<EMAIL>> 1750834868 +0800	commit: feat(domain): 优化产品 SKU 数据结构和映射
615ad5a904e04edf41311ab91e2ab7c4712e1767 dfb51d719c362c049e184ed96f0f306e0e990e2a james <<EMAIL>> 1750921890 +0800	commit: 更新 pom.xml 中 fulfillmen-support.version 版本至 1.1.6-SNAPSHOT；新增订单预览 JSON 测试文件；删除不再使用的国际化测试类和配置文件；优化国际化消息和异常处理逻辑，增强代码可读性和注释；更新多个实体类和映射类，添加最小起订量字段，完善订单相关功能。
dfb51d719c362c049e184ed96f0f306e0e990e2a ff153f3cc89c02d0612aa8f9a9d29de39696700d james <<EMAIL>> 1750947799 +0800	commit: 优化代码格式和可读性，添加最小起订量字段至多个实体类和映射类，更新购物车和订单相关功能，增强国际化消息处理，修复部分代码缩进问题。
ff153f3cc89c02d0612aa8f9a9d29de39696700d 217f114dd3b328a157ce1bce4e7c661f63215460 james <<EMAIL>> 1751019163 +0800	commit: 新增租户仓库表和相关字段，删除不再使用的 JSON 文件，优化订单相关功能，增强国际化消息处理，更新订单状态枚举，完善订单流程设计，提升代码可读性和注释。
217f114dd3b328a157ce1bce4e7c661f63215460 c0f5d1181746ab0fed79b564f4acda223cb56078 james <<EMAIL>> 1751206930 +0800	commit: feat(fulfillmen-shop-api):为 OpenapiSearchProductReq 类添加销量筛选参数
c0f5d1181746ab0fed79b564f4acda223cb56078 d4cbb612c4fe65866da0506b33607d21e0b9fe65 james <<EMAIL>> 1751207916 +0800	commit: docs(架构重构): 提交Convert包和Repository领域设计文档
d4cbb612c4fe65866da0506b33607d21e0b9fe65 eba91c6add28bcbf0987a69d650b2b272d29dd5f james <<EMAIL>> 1751208459 +0800	rebase (finish): refs/heads/develop onto 63ee941d649377c4e5a55b799d5adb81981f87bb
eba91c6add28bcbf0987a69d650b2b272d29dd5f a415a43ba25f80941e78a4a60e98a68d7ca6735d james <<EMAIL>> 1751208748 +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
a415a43ba25f80941e78a4a60e98a68d7ca6735d 15640f815fde5610688f1cbf41efeca5cbcb2584 james <<EMAIL>> 1751274226 +0800	merge origin/develop: Fast-forward
15640f815fde5610688f1cbf41efeca5cbcb2584 471126742f13c19ff10825f5ee9a3654907ccbcf james <<EMAIL>> 1751360321 +0800	commit: 更新 pom.xml 版本至 1.2.5-SNAPSHOT；在 TenantConfig.java 中增加注释以提高可读性；在 UserContextHolder.java 中将 ThreadLocal 替换为 TransmittableThreadLocal；在 BusinessExceptionI18n.java 中新增工厂方法以支持国际化异常创建；在 TzUserMapper.xml 中添加新字段以支持用户信息；新增 AlibabaCreateOrderConvert、CreateOrderDTO 和 CreateOrderRespDTO 类以支持订单创建功能；在 OrderReq.java 中添加购物车相关字段；删除 ProductSyncMonitor.java 文件；更新 OrderEventManager.java 中的事件发布逻辑；修改 OpenapiAccountRepository 接口以继承 IRepository；新增 OrderRepository 和 OrderRepositoryImpl 类以支持订单数据访问；更新 TenantResolverService.java 中的查询逻辑；在 IOrderManager 接口中更新创建订单方法返回类型；在 OrderManager.java 中更新订单创建逻辑；优化多个类中的代码格式和注释。
471126742f13c19ff10825f5ee9a3654907ccbcf 3f5053f836496a368a6052c91d2563ba6e62a4db james <<EMAIL>> ********** +0800	commit: 在 TzOrderItemMapper.xml 中新增 is_single_item 字段映射；在 TzProductMapping.java 中优化代码格式和注释，调整方法参数格式；在 CreateOrderDTO.java 中重构订单数据结构，新增采购订单和供应商订单字段；在 TzOrderItem.java 中添加 isSignleItem 字段以支持单品逻辑；在 OrderEventManager.java 中优化订单创建事件处理逻辑；在 FrontendOrderConvert.java 中更新单品处理逻辑，确保订单预览功能正常；在 OrderSubmitVO.java 中调整字段结构以提高可读性。
3f5053f836496a368a6052c91d2563ba6e62a4db 1c1a2c27465b2292131e2e6be6e8650452ad85d7 james <<EMAIL>> 1751444058 +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
1c1a2c27465b2292131e2e6be6e8650452ad85d7 7ca5ba0cd712bfaf8372103c3a9b63a67feb1330 james <<EMAIL>> 1751463965 +0800	commit: refactor(order): 重构订单创建逻辑
7ca5ba0cd712bfaf8372103c3a9b63a67feb1330 91cdbacb8d1f8d441364fc4c5fc5c1ad226c63a3 james <<EMAIL>> 1751525462 +0800	commit: feat(租户): 升级租户处理机制到过滤器级别并集成 Redis 缓存
91cdbacb8d1f8d441364fc4c5fc5c1ad226c63a3 2f8b9fd3f59ce543154c4e35f4cd0d1206e90ef6 james <<EMAIL>> 1751607950 +0800	commit: refactor(领域模型): 重构订单相关实体类
2f8b9fd3f59ce543154c4e35f4cd0d1206e90ef6 9880e859243d0f92a5a496130b3db23ddd616ffa james <<EMAIL>> 1751608036 +0800	rebase (finish): refs/heads/develop onto 1c1a2c27465b2292131e2e6be6e8650452ad85d7
9880e859243d0f92a5a496130b3db23ddd616ffa 84d9e33666197f0081dfd6618250ae4c8f588e3d james <<EMAIL>> 1751617241 +0800	commit: refactor(fulfillmen-shop):调整 API 路径和添加开发环境配置
84d9e33666197f0081dfd6618250ae4c8f588e3d d022afed02d84dc4d53897cd11ad1e46986ac9be james <<EMAIL>> 1751622088 +0800	commit: feat(order): 优化订单创建逻辑，支持服务费和价格比较- 新增服务费计算功能，根据租户配置动态调整服务费率
d022afed02d84dc4d53897cd11ad1e46986ac9be dec6cdf6a91305aea5f2d765c65ce8dd134fefee james <<EMAIL>> 1752544933 +0800	fetch origin develop:develop --recurse-submodules=no --progress --prune: fast-forward
dec6cdf6a91305aea5f2d765c65ce8dd134fefee 1dc7800da98b177a76f066434633a7f75a17785d james <<EMAIL>> 1752575408 +0800	merge origin/develop: Fast-forward
1dc7800da98b177a76f066434633a7f75a17785d de2e6419edde89199415d378c8e7a016eded0b88 james <<EMAIL>> 1752575673 +0800	commit: feat(manager): 添加 1688 支付服务适配器接口
de2e6419edde89199415d378c8e7a016eded0b88 4bca0d81015a610d1d25c311c3b31c24dc04ecdf james <<EMAIL>> 1752589237 +0800	pull --tags origin develop: Fast-forward
4bca0d81015a610d1d25c311c3b31c24dc04ecdf 38983f77802dd9d6ce41122c96a4f8b41a8340d7 james <<EMAIL>> 1752652718 +0800	commit: refactor(wms): 重构 WMS API 客户端和相关服务
38983f77802dd9d6ce41122c96a4f8b41a8340d7 5258a83c023b564fe6206c5bd022b0702fc7b667 james <<EMAIL>> 1752652786 +0800	commit: feat(support): 更新订单状态枚举
5258a83c023b564fe6206c5bd022b0702fc7b667 1167a05dcc47e69bdc7035faaf6c68d7524ea4de james <<EMAIL>> 1752652806 +0800	rebase (finish): refs/heads/develop onto 4bca0d81015a610d1d25c311c3b31c24dc04ecdf
1167a05dcc47e69bdc7035faaf6c68d7524ea4de 9a69002f43fb66d7003c48ec463faa56458313d8 james <<EMAIL>> 1752662951 +0800	merge origin/develop: Fast-forward
9a69002f43fb66d7003c48ec463faa56458313d8 72bcb51aee555ada49c44755d28d70ebaf1c323b james <<EMAIL>> 1752834769 +0800	commit: 更新 pom.xml 版本号至 1.2.7-SNAPSHOT；新增 2025-07-05 和 2025-07-17 的日志文件；在 application-dev.yml 和 application.yml 中添加 WMS 基础 URL 配置；优化线程池配置，增加相关测试类；重构订单相关实体和枚举，添加新的字段和方法以支持 WMS 订单同步功能。
72bcb51aee555ada49c44755d28d70ebaf1c323b bdb52649a1e912fdd676987676f13dbcd53eb34a james <<EMAIL>> 1752909030 +0800	commit: refactor(wms): 重构 WMS 相关代码
bdb52649a1e912fdd676987676f13dbcd53eb34a f0910e19ede05c2471f1eda350041dbaf6e6fc13 james <<EMAIL>> 1753095977 +0800	commit: 优化订单处理逻辑和代码可读性
f0910e19ede05c2471f1eda350041dbaf6e6fc13 524511f442de463e21cce548f1f6afdaecc73d01 james <<EMAIL>> 1753343186 +0800	commit: refactor(订单): 重构订单相关实体和 mapper
524511f442de463e21cce548f1f6afdaecc73d01 5c631cc53f908dc92ff24c60f922f313a61026e9 james <<EMAIL>> 1753343209 +0800	rebase (finish): refs/heads/develop onto bdb52649a1e912fdd676987676f13dbcd53eb34a
5c631cc53f908dc92ff24c60f922f313a61026e9 d72c1baba68c85cc6e7a784df9bfca19883fd373 james <<EMAIL>> 1753439155 +0800	commit: feat(订单处理): 重构订单webhook处理系统
d72c1baba68c85cc6e7a784df9bfca19883fd373 76fc6e78030c87adc4e87557639345d969c28b3a james <<EMAIL>> 1753439989 +0800	commit: test: 删除集成测试代码
76fc6e78030c87adc4e87557639345d969c28b3a 16a9f7ac66162b82d470927fbe4e3a16877a8e9c james <<EMAIL>> 1753498157 +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
16a9f7ac66162b82d470927fbe4e3a16877a8e9c a2619aae066c0824de72758e47ca0a432903fac8 james <<EMAIL>> 1754045239 +0800	fetch origin develop:develop --recurse-submodules=no --progress --prune: fast-forward
a2619aae066c0824de72758e47ca0a432903fac8 29bb4670d8e8fe112593e50194e5429dd6541958 james <<EMAIL>> 1754055876 +0800	commit: refactor(fulfillmen-shop):重构订单处理相关代码
29bb4670d8e8fe112593e50194e5429dd6541958 4b8a0796c827ed4ac5db9292ad55f4fb6d6e08bf james <<EMAIL>> 1754055999 +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
4b8a0796c827ed4ac5db9292ad55f4fb6d6e08bf 8b41c47dd911202756037e8b022f35f3c68e4738 james <<EMAIL>> 1754056039 +0800	commit: style:优化代码格式和可读性
8b41c47dd911202756037e8b022f35f3c68e4738 6d8834e5ae4147f0f8ef78076ea9a51e836a53d1 james <<EMAIL>> 1754056511 +0800	commit: refactor(fulfillmen-shop-frontend): 优化订单服务取消订单逻辑
6d8834e5ae4147f0f8ef78076ea9a51e836a53d1 d71fb6faa75143d96c4468c266f50e0de7fad780 james <<EMAIL>> 1754059124 +0800	commit: build:调整 Dockerfile 中的用户和组 ID
d71fb6faa75143d96c4468c266f50e0de7fad780 74bff04e2e7afc3aae9c460e6b38b880fe4c0197 james <<EMAIL>> 1754063538 +0800	commit: refactor(fulfillmen-shop): 重构订单上下文类及相关逻辑
74bff04e2e7afc3aae9c460e6b38b880fe4c0197 3abd72c507eefb7efff4709557811f9e67b928c1 james <<EMAIL>> 1754064165 +0800	commit: refactor(fulfillmen-shop):优化代码格式和结构
3abd72c507eefb7efff4709557811f9e67b928c1 ff5e8baed4f008baa53b16158c399b4cdeee1893 james <<EMAIL>> 1754065494 +0800	commit: refactor(fulfillmen-shop):优化代码格式和结构
ff5e8baed4f008baa53b16158c399b4cdeee1893 7e5b65c90144f321120b3f0febe1c068bc334a60 james <<EMAIL>> 1754102192 +0800	commit: refactor(order): 重构订单支付流程并优化退款申请逻辑
7e5b65c90144f321120b3f0febe1c068bc334a60 fe2341515e0b5ec2e31df69b3ec120be27b1999c james <<EMAIL>> 1754104416 +0800	commit: feat(order): 添加订单支付相关功能并优化支付流程
fe2341515e0b5ec2e31df69b3ec120be27b1999c c950f19c6cfe7299fdc36f4a25cab61415958a13 james <<EMAIL>> 1754104436 +0800	commit: refactor(fulfillmen): 代码格式化和国际化文案调整
c950f19c6cfe7299fdc36f4a25cab61415958a13 2880acd1200a1402dfaf7b8913a33f9433d5f8f1 james <<EMAIL>> 1754299474 +0800	commit: refactor(order): 优化订单处理逻辑
2880acd1200a1402dfaf7b8913a33f9433d5f8f1 d5eeae2de0f313fecf3761b7a4ca81be2964f417 james <<EMAIL>> 1754365472 +0800	commit: refactor(fulfillmen-shop):重构订单运费计算逻辑并添加测试工具
d5eeae2de0f313fecf3761b7a4ca81be2964f417 2c8b94b357a62b51a5c5abff97c0a5f8a8290e4d james <<EMAIL>> 1754374560 +0800	commit: refactor(currency): 优化货币转换逻辑并新增可选的最小价格保护功能
2c8b94b357a62b51a5c5abff97c0a5f8a8290e4d 66f4f3b3d2a0b9ecbe1cbe39710de866dff0eeb5 james <<EMAIL>> 1754377952 +0800	commit: feat(order): 优化订单预览价格计算逻辑
66f4f3b3d2a0b9ecbe1cbe39710de866dff0eeb5 00522e3e00ad9bed0cff6e01dbb7424ecbafd80d james <<EMAIL>> 1754384381 +0800	merge origin/develop: Fast-forward
00522e3e00ad9bed0cff6e01dbb7424ecbafd80d b37837bcb66a96e80d5c2a572dd957ea90ccf5b9 james <<EMAIL>> 1754388687 +0800	commit: refactor(shop): 优化订单预览功能和汇率转换逻辑- 移除了 IWmsManager接口中多余的函数注释- 优化了 FrontendOrderPurchaseConvert 中的汇率转换逻辑，使用 NumberUtil 进行空值处理和乘法运算
b37837bcb66a96e80d5c2a572dd957ea90ccf5b9 4d4ce4937ebe6398c504cd14887a5dad44a2dc84 james <<EMAIL>> 1754406147 +0800	commit: refactor(order): 优化订单数据同步和处理逻辑
4d4ce4937ebe6398c504cd14887a5dad44a2dc84 8a597144c5c6c94a61ede35e971116f995e89b63 james <<EMAIL>> 1754460422 +0800	commit: build(fulfillmen-shop): 更新 Docker 和 Git 配置
8a597144c5c6c94a61ede35e971116f995e89b63 3e682faa8ce4e394becb15abb3fe581792e6539a james <<EMAIL>> 1754460732 +0800	commit: Remove node_modules files from git tracking
3e682faa8ce4e394becb15abb3fe581792e6539a 2ca46fb911aaab05a59768d9339401eb790fd44f james <<EMAIL>> 1754460787 +0800	commit: Remove ignored files from git tracking
2ca46fb911aaab05a59768d9339401eb790fd44f 3cce30b9382553a6ab93684ce95ba79075bda0a6 james <<EMAIL>> 1754460961 +0800	commit: Remove ignored files from git tracking
3cce30b9382553a6ab93684ce95ba79075bda0a6 76c32c146fa3352c75b811e78da56544356d2320 james <<EMAIL>> 1754460986 +0800	commit: Remove ignored files from git tracking
76c32c146fa3352c75b811e78da56544356d2320 d19790feac7f4178c7d5fb09fd57a167f922dee7 james <<EMAIL>> 1754550297 +0800	commit: 更新 .gitignore 文件，添加多个文件和目录到忽略列表
d19790feac7f4178c7d5fb09fd57a167f922dee7 6f6e66c0a3412e7d318e17229fe0540ff065f559 james <<EMAIL>> 1754635194 +0800	commit: feat(alibaba): 添加阿里巴巴回调日志处理功能
6f6e66c0a3412e7d318e17229fe0540ff065f559 32d37552f78c98115fc29374543d84e500b15268 james <<EMAIL>> 1754637245 +0800	rebase (finish): refs/heads/develop onto 76c32c146fa3352c75b811e78da56544356d2320
32d37552f78c98115fc29374543d84e500b15268 b02da19d33f316354277c7e7f222316974035b10 james <<EMAIL>> 1754638871 +0800	commit: 优化代码格式和注释，增强可读性
b02da19d33f316354277c7e7f222316974035b10 eff8ea329086a39c72cfe88f0bc1e1c5f5a9b0fb james <<EMAIL>> 1754638892 +0800	merge origin/develop: Merge made by the 'ort' strategy.
eff8ea329086a39c72cfe88f0bc1e1c5f5a9b0fb f67fff22fd264226496507d4a7beef0585d205ca james <<EMAIL>> 1754639030 +0800	merge origin/develop: Fast-forward
f67fff22fd264226496507d4a7beef0585d205ca 41920b4759726009f619d5b19eab08a1455061d8 james <<EMAIL>> 1754640904 +0800	commit: fix(fulfillmen-shop-manager):修复 1688 订单创建时运费显示不正确的问题- 在 OrderCreationProcessor 中添加实际支付运费的设置
41920b4759726009f619d5b19eab08a1455061d8 d02f517fded80201d3b38252ad8621905c1d2248 james <<EMAIL>> 1754640919 +0800	commit: refactor(fulfillmen-shop-manager): 调整代码结构并优化导入顺序
d02f517fded80201d3b38252ad8621905c1d2248 446657077c67bc52a6db43c1f4e5c7cec4a701c3 james <<EMAIL>> 1754969359 +0800	commit: refactor(shop): 重构订单数据同步逻辑
446657077c67bc52a6db43c1f4e5c7cec4a701c3 ebe40b862d299537a802f1d958f7bba9eb767d49 james <<EMAIL>> 1754979531 +0800	commit: feat(fulfillmen-shop): 添加阿里巴巴回调日志表的租户配置并优化相关定时任务
ebe40b862d299537a802f1d958f7bba9eb767d49 057b63bbbafef368a1d845f3aff5685cdfeba3e9 james <<EMAIL>> 1754979545 +0800	merge origin/develop: Merge made by the 'ort' strategy.
057b63bbbafef368a1d845f3aff5685cdfeba3e9 b704c35d288fd9dc9fcb7317433b638c6dfbc192 james <<EMAIL>> 1754983221 +0800	commit: feat(fulfillmen-shop): 重构阿里巴巴回调日志处理逻辑
b704c35d288fd9dc9fcb7317433b638c6dfbc192 5dd715dd14ae6afda5312be24365fc580b9d5c4c james <<EMAIL>> 1755150528 +0800	commit: feat(domain): 新增 SysAlibabaCallbackLogsProcessStatusEnum 枚举类
5dd715dd14ae6afda5312be24365fc580b9d5c4c 247ff5e1c94cb4af76f93077e42ebade65e62a53 james <<EMAIL>> 1755150921 +0800	commit: refactor(fulfillmen-shop-manager): 优化订单回调日志创建时间
247ff5e1c94cb4af76f93077e42ebade65e62a53 e21e5bc0ceedbe1c767d143a079cb3ba76ed9aa5 james <<EMAIL>> 1755150929 +0800	rebase (finish): refs/heads/develop onto b704c35d288fd9dc9fcb7317433b638c6dfbc192
e21e5bc0ceedbe1c767d143a079cb3ba76ed9aa5 a0ac1cd836d9b7d09ab5ee90650b1e67ff7755ca james <<EMAIL>> 1755226796 +0800	cherry-pick: refactor(backend): 重构产品同步服务
a0ac1cd836d9b7d09ab5ee90650b1e67ff7755ca a1ff6577916814862a7652eb74b32de4f10a4412 james <<EMAIL>> 1755229876 +0800	merge origin/develop: Fast-forward
a1ff6577916814862a7652eb74b32de4f10a4412 9557ed3a1d5c9d2f2ac56c662c18f5146356a232 james <<EMAIL>> 1755250482 +0800	commit: refactor(order): 重构订单创建逻辑
9557ed3a1d5c9d2f2ac56c662c18f5146356a232 ec9de1a907d5b518d3f7d881e779da56c4b4cfc3 james <<EMAIL>> 1755250497 +0800	commit: refactor(fulfillmen-shop):优化代码格式和结构
ec9de1a907d5b518d3f7d881e779da56c4b4cfc3 75e7429468a01fe69dbe3ce72d634dda5309dacb james <<EMAIL>> 1755250509 +0800	rebase (finish): refs/heads/develop onto a1ff6577916814862a7652eb74b32de4f10a4412
75e7429468a01fe69dbe3ce72d634dda5309dacb d0cf84bff8b6dda2c37d60747ad7c93560e73d14 james <<EMAIL>> 1755251229 +0800	merge origin/develop: Fast-forward
d0cf84bff8b6dda2c37d60747ad7c93560e73d14 755540a599b983d84a39873482f92113c4173f22 james <<EMAIL>> 1755251394 +0800	commit: refactor(fulfillmen-shop):优化代码格式和结构
755540a599b983d84a39873482f92113c4173f22 84b47c5d4494f2bc33b5501176d9a581e4274f05 james <<EMAIL>> 1755310110 +0800	commit: refactor(shop): 重构部分代码并优化项目结构
84b47c5d4494f2bc33b5501176d9a581e4274f05 1050205cc36d290474c11bd6c36da495afd926e7 james <<EMAIL>> 1755310119 +0800	merge origin/develop: Merge made by the 'ort' strategy.
1050205cc36d290474c11bd6c36da495afd926e7 ec335a9dd81830132e46cc84da0d2396d601d61e james <<EMAIL>> 1755310387 +0800	commit: style:优化代码格式和缩进
