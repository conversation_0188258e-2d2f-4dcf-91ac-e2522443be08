0000000000000000000000000000000000000000 357a7c5194d2cf9c740ff36735b2030ab7d252d2 zhangmz <<EMAIL>> 1739265407 +0800	commit (initial): feat: 添加项目配置和工具支持
357a7c5194d2cf9c740ff36735b2030ab7d252d2 fec9f7ba48459037a62dc3bee9fbcaaef35c6a7d james <<EMAIL>> 1739417600 +0800	commit: refactor: Update GoodsDetailResponse with comprehensive data model improvements
fec9f7ba48459037a62dc3bee9fbcaaef35c6a7d a9c0b38993c21351321fdbc5b568084331dbd0e0 james <<EMAIL>> 1740048502 +0800	commit: feat(shop): 重构分类菜单并添加新功能
a9c0b38993c21351321fdbc5b568084331dbd0e0 acf82ed227b4f012d351c2265f6931ea1617bf9d james <<EMAIL>> 1740642575 +0800	commit: refactor(fulfillment-support):调整商品推荐接口参数- 移除了 GoodsRecommendRequestRecord 中的 offerId 字段
acf82ed227b4f012d351c2265f6931ea1617bf9d c47378def2fa8ef64f0c96c677bdb4d816e327ff james <<EMAIL>> 1741408868 +0800	commit: build(fulfillmen-shop): 更新构建配置和数据库设置
c47378def2fa8ef64f0c96c677bdb4d816e327ff a7f5a6100dac1e5debdfe3b2b582a4b36a7c068e james <<EMAIL>> 1741409030 +0800	commit (amend): 移除 toolschain
a7f5a6100dac1e5debdfe3b2b582a4b36a7c068e bc9f39dbb69a223825681ff81323d753577a368b james <<EMAIL>> 1741592163 +0800	commit: refactor(fulfillmen-shop):重构项目结构和配置
bc9f39dbb69a223825681ff81323d753577a368b b2dc5c709a32f583a8d03799c6bf347e177326ed james <<EMAIL>> 1745912821 +0800	commit: build(fulfillmen): 更新 MyBatis Plus 版本并集成 CosId
b2dc5c709a32f583a8d03799c6bf347e177326ed f82e1391c2f93bd19220768b8aedba393872808b james <<EMAIL>> 1747036921 +0800	commit: feat(购物车): 重构购物车相关功能
f82e1391c2f93bd19220768b8aedba393872808b c40c0bd70ef12842cb7e88c5890e8b8521e3f77e james <<EMAIL>> 1748395714 +0800	commit: docs(fulfillmen-shop): 添加依赖管理相关文档
c40c0bd70ef12842cb7e88c5890e8b8521e3f77e 7b6a6c5ce2a336250d386b5321e7f26b0678105b james <<EMAIL>> 1748421788 +0800	commit: build(fulfillmen-support): 优化项目构建和代码质量
7b6a6c5ce2a336250d386b5321e7f26b0678105b 56faad26b82a540072b8cbab4cd70610aa861c44 james <<EMAIL>> 1748946725 +0800	commit: feat(deploy): 新增自动化部署脚本和版本管理功能
56faad26b82a540072b8cbab4cd70610aa861c44 8ba4e746435062a27e4a769ee4c925d0a4b756d8 james <<EMAIL>> 1748946758 +0800	rebase (finish): refs/heads/master onto a7f5a6100dac1e5debdfe3b2b582a4b36a7c068e
8ba4e746435062a27e4a769ee4c925d0a4b756d8 77d6e6023c54cef44dbc83638ace49ccb06a8cf6 james <<EMAIL>> 1749552670 +0800	commit: refactor(pom): 更新项目依赖和构建配置
77d6e6023c54cef44dbc83638ace49ccb06a8cf6 e41096226c4f50027536db8719b40043d5e001a7 james <<EMAIL>> 1750665515 +0800	commit: build(fulfillmen-support-alibaba): 更新项目版本并优化代码
e41096226c4f50027536db8719b40043d5e001a7 8c5245858791fe08dbcbd6b380d699d095ff8ab5 james <<EMAIL>> 1750665539 +0800	rebase (finish): refs/heads/master onto 8ba4e746435062a27e4a769ee4c925d0a4b756d8
8c5245858791fe08dbcbd6b380d699d095ff8ab5 0b1d42a26ba30754de752e5e21fbb269c031074c james <<EMAIL>> 1750665728 +0800	commit: build: 更新 Fulfillmen Support模块版本
0b1d42a26ba30754de752e5e21fbb269c031074c 70f90e291e800b32961dafb37b0325bb2d4e02c9 james <<EMAIL>> 1750921919 +0800	commit: build: 更新 Fulfillmen Support 模块版本至 1.1.6-SNAPSHOT
70f90e291e800b32961dafb37b0325bb2d4e02c9 0ac14d977a4999de79897ac329eb255f9a2c3b83 james <<EMAIL>> 1751019183 +0800	commit: feat: 添加 OrderCreateRequestRecord 的参数校验和转换方法
0ac14d977a4999de79897ac329eb255f9a2c3b83 d8cbc2a44a03ab7994dbbd38047b9cdf7f3030cc james <<EMAIL>> 1751205160 +0800	commit: feat(goods): 商品搜索添加销量筛选功能
d8cbc2a44a03ab7994dbbd38047b9cdf7f3030cc 3e93cadfb3a0e96432ca1aae8fea74d2082b6aa9 james <<EMAIL>> 1751348831 +0800	commit: build(fulfillmen): 更新版本号并添加 Guava 依赖
3e93cadfb3a0e96432ca1aae8fea74d2082b6aa9 591d90001770da26655db709a411df83d7262e97 james <<EMAIL>> 1751348965 +0800	rebase (finish): refs/heads/master onto 0b1d42a26ba30754de752e5e21fbb269c031074c
591d90001770da26655db709a411df83d7262e97 87b930b6bc29fed5b9494d91b799c7746ff7b10a james <<EMAIL>> 1751426088 +0800	commit: refactor(fulfillmen-support-alibaba): 优化商品规格ID校验逻辑
87b930b6bc29fed5b9494d91b799c7746ff7b10a 4f66f0d294a8210394c8aa5cda14dd609b3e0f14 james <<EMAIL>> 1751463965 +0800	commit: refactor(order): 重构订单创建逻辑
4f66f0d294a8210394c8aa5cda14dd609b3e0f14 637af681480ebdae022878d8cc5082d1c84e71b6 james <<EMAIL>> 1752145450 +0800	commit: feat(fulfillmen-support-alibaba): 添加阿里巴巴回调消息处理功能
637af681480ebdae022878d8cc5082d1c84e71b6 169c43f492a37a16acd83f4a9155178d0044a72e james <<EMAIL>> 1752209572 +0800	commit: refactor(fulfillmen-support-alibaba): 重构消息分发和签名验证逻辑
169c43f492a37a16acd83f4a9155178d0044a72e b6c1de1048baa59764ad16150578aaba72ef1723 james <<EMAIL>> 1752225029 +0800	commit: docs(fulfillmen-support): 补充和完善消息类型的文档注释- 在 GoodsMessageTypeEnums、LogisticsMessageTypeEnums 和 OrderMessageTypeEnums 类中添加了详细的消息格式说明
b6c1de1048baa59764ad16150578aaba72ef1723 26241edc754fb720c7378967bcf8da1c7cfbb244 james <<EMAIL>> 1752332355 +0800	commit: feat(fulfillmen-support-alibaba): 增强订单和商品信息模型，支持日期反序列化
26241edc754fb720c7378967bcf8da1c7cfbb244 9f50a7d17cd356e7a56ba8d52ab11bf825ea9208 james <<EMAIL>> 1752542976 +0800	rebase (finish): refs/heads/master onto 637af681480ebdae022878d8cc5082d1c84e71b6
9f50a7d17cd356e7a56ba8d52ab11bf825ea9208 81db7db9c834224703c4aca0d0c2a7d8e9376adb james <<EMAIL>> 1752547303 +0800	commit: refactor(fulfillmen): 重构 Webhook API 和订单处理逻辑- 重构 WebhookApi 类，使用构造器注入代替自动注入- 重命名 OrderCreateHandler为 OrderHandler，优化订单处理逻辑
81db7db9c834224703c4aca0d0c2a7d8e9376adb 63646b61d4392ee655a075f0bb3bdde2c2956675 james <<EMAIL>> 1752652786 +0800	commit: feat(support): 更新订单状态枚举
63646b61d4392ee655a075f0bb3bdde2c2956675 8f2582e0f4e5a0eac0f33e0d9bdd39c111fb8c5e james <<EMAIL>> 1753069351 +0800	commit: style(fulfillmen-support-alibaba):
8f2582e0f4e5a0eac0f33e0d9bdd39c111fb8c5e e09d0b64bc27ce262977ea54ace41418287a7c53 james <<EMAIL>> 1753081942 +0800	commit: refactor(fulfillmen-support-alibaba): 重构阿里巴巴开放平台 SDK- 移除冗余代码和不必要的依赖
e09d0b64bc27ce262977ea54ace41418287a7c53 81ae0f60e1192e7107992c52fd3b109ecf36bfb7 james <<EMAIL>> 1753095994 +0800	commit: 更新版本号至 1.2.0-SNAPSHOT，新增 fulfillmen-support-common 模块，优化项目结构和代码可读性，添加通用 API 请求执行器和响应处理器，增强 WMS API 支持，确保各模块间的兼容性和可扩展性。
81ae0f60e1192e7107992c52fd3b109ecf36bfb7 474fb34228795eb0022fa7e2afdea5d023bd30cc james <<EMAIL>> 1753342917 +0800	commit: refactor(fulfillmen-support-alibaba): 重构订单相关模型和文档
474fb34228795eb0022fa7e2afdea5d023bd30cc aff2fbd57395316203412360e5edc663167ddeca james <<EMAIL>> 1753342944 +0800	rebase (finish): refs/heads/master onto 8f2582e0f4e5a0eac0f33e0d9bdd39c111fb8c5e
aff2fbd57395316203412360e5edc663167ddeca 32410abadbf9c4e6c8f7d42ecbf3393b4d6ed87f james <<EMAIL>> 1753343030 +0800	commit: ci:优化代码格式和注释
32410abadbf9c4e6c8f7d42ecbf3393b4d6ed87f acfc5249179cc48dfaed2c6500c14dfe92be095c james <<EMAIL>> 1753410159 +0800	commit: feat(fulfillmen-starter-json-jackson): 新增多格式时间支持模块
acfc5249179cc48dfaed2c6500c14dfe92be095c 621299f968360736b79439ecaf43cb54fcdf57bb james <<EMAIL>> 1753440002 +0800	commit: refactor(fulfillmen-support):重构时间格式处理和 Web 客户端配置
621299f968360736b79439ecaf43cb54fcdf57bb 786182ef7d6d544da3985bcaf113a945bf42edb4 james <<EMAIL>> 1753698809 +0800	commit: feat(fulfillmen-support-alibaba): 增强订单和商品处理功能
786182ef7d6d544da3985bcaf113a945bf42edb4 5a4cb137019df0c9bd9adf687e5b5c824beeb9e8 james <<EMAIL>> 1753786275 +0800	commit: 更新版本号至 1.2.1-SNAPSHOT，新增请求基接口和 WebClient 请求执行器示例，优化异常处理和日志记录功能，重构部分请求和响应类，增强代码可读性和可维护性。
5a4cb137019df0c9bd9adf687e5b5c824beeb9e8 15d390fcc2fdef374993d4d1b1efa58220eb859b james <<EMAIL>> 1753956418 +0800	commit: feat(wms): 新增入库管理相关 API 接口和数据结构
15d390fcc2fdef374993d4d1b1efa58220eb859b fd891bb215c39964df3d33c816837adb2e3d5513 james <<EMAIL>> 1754039287 +0800	commit: feat(wms): 优化 WMS 客户端配置并添加 SSL 支持
fd891bb215c39964df3d33c816837adb2e3d5513 bfe64dc90cc8a49c17d80fe0bcdcb1c9c1578448 james <<EMAIL>> 1754039393 +0800	rebase (finish): refs/heads/master onto 5a4cb137019df0c9bd9adf687e5b5c824beeb9e8
bfe64dc90cc8a49c17d80fe0bcdcb1c9c1578448 2048eea12aab5e902ac0de0e81f048819a46c232 james <<EMAIL>> 1754063563 +0800	commit: 优化 WMS API 客户端代码格式，增强可读性
2048eea12aab5e902ac0de0e81f048819a46c232 ca0fc22d8edd14987146d183af219afabbb2225e james <<EMAIL>> 1754063840 +0800	commit: refactor(wms): 重构 API 调用中的金额表示方式
ca0fc22d8edd14987146d183af219afabbb2225e 22df1a6ae863f0f9d4bf3419c024052e54bcebdf james <<EMAIL>> 1754100544 +0800	commit: refactor(fulfillmen):调整采购订单详情接口参数名称
22df1a6ae863f0f9d4bf3419c024052e54bcebdf bb8db9255c5f2b59d38bd51c6e0517147786bd0c james <<EMAIL>> 1754104416 +0800	commit: feat(order): 添加订单支付相关功能并优化支付流程
bb8db9255c5f2b59d38bd51c6e0517147786bd0c fe5e4e70ad9a413246e8a9e60a538bc576a30dc0 james <<EMAIL>> 1754104437 +0800	commit: refactor(fulfillmen): 代码格式化和国际化文案调整
fe5e4e70ad9a413246e8a9e60a538bc576a30dc0 ca5cc57ac8805a79a0112783cf67907b822d037d james <<EMAIL>> 1754460422 +0800	commit: build(fulfillmen-shop): 更新 Docker 和 Git 配置
ca5cc57ac8805a79a0112783cf67907b822d037d 338a82619a68cf112b285fd26f1f8e25b8e05848 james <<EMAIL>> 1754635205 +0800	commit: 优化订单退款状态枚举，增加从代码获取枚举的方法；更新订单状态枚举，添加已收货状态；增强订单消息类的注释，增加退款和发货相关状态；调整 WMS API 接口和测试代码格式，提高可读性。
338a82619a68cf112b285fd26f1f8e25b8e05848 1a398e942402aa3eee41de03f9358a0bb1c9d90f james <<EMAIL>> 1754640904 +0800	commit: fix(fulfillmen-shop-manager):修复 1688 订单创建时运费显示不正确的问题- 在 OrderCreationProcessor 中添加实际支付运费的设置
1a398e942402aa3eee41de03f9358a0bb1c9d90f 7a421f6500ae68c70ed024037afb360d1e95c9ae james <<EMAIL>> 1755248162 +0800	commit: refactor(fulfillmen-shop):重构阿里巴巴回调日志处理逻辑- 新增 AlibabaCallbackProcessingService公共服务类，用于处理回调日志
7a421f6500ae68c70ed024037afb360d1e95c9ae a9d8397c701ddf0767be0c389577bfe8376446b0 james <<EMAIL>> 1755310110 +0800	commit: refactor(shop): 重构部分代码并优化项目结构
