DIRC     qhTՆ
A�ZhTՆ
A�Z  RI�  ��  �     JV��jD�v�#���_$H�Jw�g .cursorrules      h���&Lh���&L  ��  ��  �     �@�F�3̇@���f�Y�$D 
.dockerignore     h��� P9yh��� P9y  ��  ��  �     Ao����/=�8��XQ�`�� 
.editorconfig     h�����h�����  ��  ��  �    ���6G�q�Я�vܔ6u�� .flattened-pom.xml        h05X�`h05X�`  �|k  ��  �      &;Ah*�y��fZ�M�ڦ��q!� .gitattributes    h��	1:L�h��	1:L�  �6   ��  �     
��(-�rqn'�n��tB;�:�H 
.gitignore        haP
J�haP
J�  v��  ��  �     ��pi��Ȅ�J���q��DO��� .style/FulfillmenJavaStyle.xml    h0K�E$h05Z�M  �|n  ��  �       �⛲��CK�)�wZ���S� &.style/Java开发手册(黄山版).pdf    h05[��h05[��  �|o  ��  �      O\�|O�"}Y�"Q�.;�c .style/license-header     h05]s�h05]s�  �|p  ��  �     �>�`�ց ��Fy���Ii� .style/p3c-codestyle.xml  h0>m��h0>m��  ��  ��  �     �7�/��16Q
�r�U�s# 
Dockerfile        h��=
7@Wh��=
7@W  �so  ��  �     �O߮�2F��R���z!v�oW� Dockerfile_aliyun h0>m��-h0>m��-  ��  ��  �     ��"�
	��Xߘ��o�CQZ> LICENSE   h0>m�h0>m�  ��  ��  �     !�g�Ϻ�+�{GE[*�`�OX� build-image.sh    h���!0�xh���!0�x  �sp  ��  �     s�)㚂ҷ�v��_,��!o� build-tools/README.md     h���!4_ih���!4_i  �sq  ��  �     V��n0ʍ�=�&��#�k .build-tools/code-style-verification-report.txt    h��� W�yh��� W�y  ��  ��  �     ,���M��aM,֚Ђ���� 1build-tools/docs/code-style-verification-guide.md h���!;�\h���!;�\  �st  ��  �     ���3�}���E��!��2�K7� )build-tools/github-actions-code-style.yml h���!?�h���!?�  �su  ��  �     �:UY�JŮC�R:�t��a� *build-tools/gradle-spotless-example.gradle        h���!D�h���!D�  �sv  ��  �     =0daqv9:�Y�+ɵ`M�� .build-tools/maven-formatter-plugin-example.xml    h5x9=d�h5x9<P�  ���  ��  �     �RԾ��R�(eь��S' V� build-tools/pom.xml       h��� ]��h��� ]��  ��  ��  �     �NZ�t1��z��~mb%����\ =build-tools/src/main/resources/config/FulfillmenJavaStyle.xml     h0>m��Ch0>m��C  ��  ��  �      O\�|O�"}Y�"Q�.;�c 4build-tools/src/main/resources/config/license-header      h0>m�v�h0>m�v�  ��  ��  �     �>�`�ց ��Fy���Ii� 7build-tools/src/main/resources/config/p3c-codestyle.xml   h���!S2h���!S2  �s~  ��  �     �5�X˽G�_��'$�nYLt� Ibuild-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java h�tK;Nh�h�tK;Nh�  �s  ��  �     &������X�+b�K�Wd�  build-tools/verify-code-style.sh  h�FQ:�#h�FQ:�#  ��  ��  �     E{� 9�g&I��lLã�M1��� docker-compose.yml        h0>m��$h0>m��$  ��  ��  �     N�T�H5�vl����&�9w�� docker/docker-compose.yml h?����h?����  7K  ��  �     x.��)�nm/ƃgFN���(� =fulfillmen-shop-api/fulfillmen-shop-openapi-service/README.md     h��+� -h��+� -  w�  ��  �     c�2G.��N�����դ�� ;fulfillmen-shop-api/fulfillmen-shop-openapi-service/pom.xml       h��'
�S�h��'
�S�  �  ��  �     �b�BU�(�$o���ʗ�00�� �fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/convert/OpenapiCertificateListVO.java       h��'
�Kh��'
�K  �  ��  �     >A��9%t�	
�X��`�� �fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/convert/OpenapiProductConvertMapping.java   hAO|!-��hAO|!-��  S2  ��  �     ����-�S`���hz��(N�� ufulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/enums/LanguageEnum.java     hbR�(�b!hbR�(�b!  x��  ��  �     ���Gev��#ş���5��O ~fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/req/OpenapiSearchProductReq.java    hAO|!6f~hAO|!6f~  S5  ��  �     �5��:%�L���UZG
V� �fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/service/OpenapiAccountService.java  haP
7m�haP
7m�  v��  ��  �     Vw�m�j)T�*����
��{o �fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/service/OpenapiProductService.java  hgj�+մhgj�+մ  � �  ��  �     �]�������Q�{�*��1M�I �fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/service/impl/OpenapiAccountServiceImpl.java hgj�+�9�hgj�+�9�  � �  ��  �     �~޹#��B�8˽��^�Nw� �fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/service/impl/OpenapiProductServiceImpl.java h��'
ߢ�h��'
ߢ�  �	  ��  �     �-�Y����a?�<�Do�d�� |fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/vo/OpenapiProductDetailVO.java      hBN�r��hBN�r��  S;  ��  �     �M����x�>���=9�^�N\^ zfulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/vo/OpenapiProductInfoVO.java        hAO|!I��hAO|!I��  S<  ��  �     |]�s0�$�p�n�Q-��c+ }fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/vo/OpenapiProductSkuSpecVO.java     hAO|!K��hAO|!K��  S=  ��  �     ����n�4i���lI;�cc }fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/vo/OpenapiSellerDataInfoVO.java     haP
7x�-haP
7x�-  v��  ��  �     � sEC�y�w����r�/E�F> {fulfillmen-shop-api/fulfillmen-shop-openapi-service/src/main/java/com/fulfillmen/shop/openapi/vo/OpenapiShippingInfoVO.java       hU)+��hU)+��  T�>  ��  �     b1������0��	�+�X~� 8fulfillmen-shop-api/fulfillmen-shop-openapi/CHANGELOG.md  hC�+�XhC�+�X  "*  ��  �     $�͇���8�T��Zh�p�� 5fulfillmen-shop-api/fulfillmen-shop-openapi/README.md     h��$.t�h��$.t�  "+  ��  �     `z�O�x��i`!�$�y*j 3fulfillmen-shop-api/fulfillmen-shop-openapi/pom.xml       hAO}�1hAO}�1  S�  ��  �     �@�i���\)���l��� vfulfillmen-shop-api/fulfillmen-shop-openapi/src/main/java/com/fulfillmen/shop/openapi/annotation/RequireInterface.java    hAO|!PJ�hAO|!PJ�  S?  ��  �     	ռ����\o��Wu^�Wi� xfulfillmen-shop-api/fulfillmen-shop-openapi/src/main/java/com/fulfillmen/shop/openapi/context/OpenapiRequestContext.java  haP
7�i�haP
7�i�  v��  ��  �     ��~F���
C�m=�ì ~fulfillmen-shop-api/fulfillmen-shop-openapi/src/main/java/com/fulfillmen/shop/openapi/controller/OpenapiProductController.java    haP
7�I�haP
7�I�  v��  ��  �     ,c[�V����nIXY�
F+
� �fulfillmen-shop-api/fulfillmen-shop-openapi/src/main/java/com/fulfillmen/shop/openapi/controller/OpenapiRateLimitTestController.java      hAO}��qhAO}��q  S�  ��  �     W!���仗�	YQy���Q� {fulfillmen-shop-api/fulfillmen-shop-openapi/src/main/java/com/fulfillmen/shop/openapi/controller/OpenapiTestController.java       haP
7��jhaP
7��j  v��  ��  �     =R1�6B͸��ED�M��ޒ� wfulfillmen-shop-api/fulfillmen-shop-openapi/src/main/java/com/fulfillmen/shop/openapi/intercept/OpenapiInterceptor.java   hC�2��#hC�2��#  S�  ��  �     m���'��tI/���rG�| qfulfillmen-shop-api/fulfillmen-shop-openapi/src/test/java/com/fulfillmen/shop/openapi/config/TestApplication.java hAO|!��(hAO|!��(  SR  ��  �     7I2��XJU��!�BI���|H� �fulfillmen-shop-api/fulfillmen-shop-openapi/src/test/java/com/fulfillmen/shop/openapi/controller/OpenapiProductControllerTest.java        haP
7��haP
7��  v��  ��  �     @8L�~n� t�ˊ6?T+t<KX �fulfillmen-shop-api/fulfillmen-shop-openapi/src/test/java/com/fulfillmen/shop/openapi/controller/RateLimitTargetVerificationTest.java     haP
7�,;haP
7�,;  v��  ��  �     ?�����B�
it��a ۂ�� �fulfillmen-shop-api/fulfillmen-shop-openapi/src/test/java/com/fulfillmen/shop/openapi/controller/SignatureTypeIntegrationTest.java        hTՆ
���hTՆ
���  RI�  ��  �     #!�W��"��d�S���2� }fulfillmen-shop-api/fulfillmen-shop-openapi/src/test/java/com/fulfillmen/shop/openapi/controller/SimpleSignatureTypeTest.java     haP
7��haP
7��  v��  ��  �     CP�r�
��<Qy�,��:�ܩ&� }fulfillmen-shop-api/fulfillmen-shop-openapi/src/test/java/com/fulfillmen/shop/openapi/controller/SimplifiedRateLimitTest.java     hC�+*
�hC�+*
�  "3  ��  �     �}+��ET��s�ֻ;d��P� Sfulfillmen-shop-api/fulfillmen-shop-openapi/src/test/resources/application-test.yml       hC�++hC�++  "4  ��  �     W�Y��@1=�oh�D�5�� Nfulfillmen-shop-api/fulfillmen-shop-openapi/src/test/resources/application.yml    hAO|!�&?hAO|!�&?  S\  ��  �     �7H��r}�	Б�����5� Lfulfillmen-shop-api/fulfillmen-shop-openapi/src/test/resources/test-data.sql      hu��$G��hu��$G��  ���  ��  �     �x���#�Y+l�.n�VI�{\ :fulfillmen-shop-bootstrap/docs/MDC过滤器使用说明.md        hu��$H�Whu��$H�W  ���  ��  �     ���4NǷ������1��XGu :fulfillmen-shop-bootstrap/docs/MDC过滤器重构说明.md        hu��$J��hu��$J��  ���  ��  �     G�o/'�k�I:HLƦ���.�aj Dfulfillmen-shop-bootstrap/docs/SaToken上下文问题解决方案.md      hu��$L/�hu��$L/�  ���  ��  �     �g�y��Ԥ�~3ҢCpDt =fulfillmen-shop-bootstrap/docs/统一过滤器配置说明.md     hAwx���hAwx���  wb  ��  �     2� �'�Gj�W�	�`� !fulfillmen-shop-bootstrap/pom.xml h0>m��hh0>m��h  ��  ��  �     w#�� ���.�#�Ȝ.��rY�� 7fulfillmen-shop-bootstrap/src/main/docker/entrypoint.sh   hy��+{{"hy��+{{"  v��  ��  �     
v�����y�T���j�K�nh� Ufulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/BootstrapApplication.java     hg��427~hg��427~  �!  ��  �     .��B"N�������K����s6 ^fulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/FulfillmenWebMvcConfig.java    hC�+,lLhC�+,lL  "5  ��  �     l�i������ԧ�w��0 Sfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/RedisConfig.java       h��C1>�h��C1>�  ���  ��  �     ʶ�����OfS�� 6:Vs�*g Vfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/SpringTxConfig.java    h��'�� h��'��   ��  ��  �     9Um<a�R[��&/�"�2�� Tfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/TenantConfig.java      hj�� �hj�� �  ���  ��  �     
`ǿp_�ܨ���wO47�5G lfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/filter/FilterConfigurationProperties.java      hj��@k�hj��@k�  ���  ��  �     ��N�9�t�
���7
�1� cfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/filter/FilterOrderConstants.java       hv-�0[��hv-�0[��  ��%  ��  �     �P��B�K�?Г����ʬ0v% ^fulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/filter/GlobalMDCFilter.java    h���
�{Dh���
�{D  �s"  ��  �     $~Nq��3��y�E�iB��O_� [fulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/filter/TenantFilter.java       hv-�0`,hv-�0`,  ��'  ��  �     H 	�{��tp�I��SB9�n� ifulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/filter/UnifiedFilterConfiguration.java hgj�-`�hgj�-`�  �!  ��  �     /�v
b� ���VP�E/�2�. ffulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/config/satoken/SaExtensionInterceptor.java    haP
7�0�haP
7�0�  v��  ��  �     eW�q��ٝ�5���N�X� bfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/controller/CurrencyRateController.java        h9ן6��Hh9ן6��H  vc  ��  �     v��a={�˿.(�CV��$�� cfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/controller/IndexRedirectController.java       haS*ʼ�haS*ʼ�  v�u  ��  �     <�gdr����U��+�=��Κ� jfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/controller/TestGracefulResponseController.java        hj���Yhj���Y  ���  ��  �     
�ƖG��?�����7�;��� ]fulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/controller/TestMDCController.java     h���*qߝh���*qߝ  �6W  ��  �     ���ȑ0��< ;�s�/���% jfulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/secheduler/CurrencyRateCacheScheduledTask.java        h��� o��h��� o��  ��  ��  �     �A��/2��T1N�O9K$k�5� ofulfillmen-shop-bootstrap/src/main/java/com/fulfillmen/shop/secheduler/SysAlibabaCallbackLogsScheduledTask.java   h��� uŨh��� uŨ  ��  ��  �     �hI�ug��vă�R���p� @fulfillmen-shop-bootstrap/src/main/resources/application-dev.yml  hh��5�hh��5�  �Ɏ  ��  �     
�C��l�9:Z�ח,��]�@� Kfulfillmen-shop-bootstrap/src/main/resources/application-filter-example.yml       h���!\��h���!\��  �s�  ��  �     I��|�&��l�x�ĺ� Bfulfillmen-shop-bootstrap/src/main/resources/application-local.yml        hV?'
n�hV?'
n�  R�y  ��  �     Ֆ����b�P�x����!ʮ Cfulfillmen-shop-bootstrap/src/main/resources/application-sealos.yml       h��P7mh��P7m  �  ��  �     �^�X�*L���-\��t�}̺ Ffulfillmen-shop-bootstrap/src/main/resources/application-sealosDev.yml    h��P7�%h��P7�%  �  ��  �     TƂK��@T e�­��ȫCf�Ѹ <fulfillmen-shop-bootstrap/src/main/resources/application.yml      h?���h?���  7$  ��  �     ʽ�v&��C������2 7fulfillmen-shop-bootstrap/src/main/resources/banner.txt   h0>m�Z�h0>m�Z�  �
  ��  �    eO�lvzj)�5��=�9��a1O% 7fulfillmen-shop-bootstrap/src/main/resources/china.json   hTڐ#$4�hTڐ#$4�  R�|  ��  �      q�l��+���(
�ְ�Y�U� :fulfillmen-shop-bootstrap/src/main/resources/db/readme.mdc        h0>m�jh0>m�j  �
	  ��  �     <.2`@H���	�~���-Kj�ڴ 8fulfillmen-shop-bootstrap/src/main/resources/favicon.ico  haS,�v%haS,�v%  v�  ��  �     .]V�(r�\������8�� Ofulfillmen-shop-bootstrap/src/main/resources/i18n/ValidationMessages.properties   haS,(��haS,(��  v��  ��  �     ~�6�Q��Yյ����}���t Ufulfillmen-shop-bootstrap/src/main/resources/i18n/ValidationMessages_zh_CN.properties     h�w,�h�w,�  ¢  ��  �     �᳏y!_
R�N�,��5 Efulfillmen-shop-bootstrap/src/main/resources/i18n/messages.properties     h�}Th�}T  £  ��  �     0�z��$��f#�`�PW Kfulfillmen-shop-bootstrap/src/main/resources/i18n/messages_zh_CN.properties       hTՆ
�-uhTՆ
�-u  RI�  ��  �     M��<Fjp=��4$�0�g�1� Lfulfillmen-shop-bootstrap/src/main/resources/i18n/openapi-message.properties      hTՆ
�.�hTՆ
�.�  RI�  ��  �     ��D���k��"-�{L����P Rfulfillmen-shop-bootstrap/src/main/resources/i18n/openapi-message_zh_CN.properties        h��� {8�h��� {8�  ��  ��  �     ~
M��.խ=Zl{���UG� ?fulfillmen-shop-bootstrap/src/main/resources/logback-spring.xml   h0>m��h0>m��  �
  ��  �     )��\l�R�G��B�'���� 8fulfillmen-shop-bootstrap/src/main/resources/private.key  h0>m��h0>m��  �
  ��  �     �;CCt3����B��,�9� 7fulfillmen-shop-bootstrap/src/main/resources/public.key   h0>m���h0>m���  �
  ��  �     �J+4|2s�Y]�S(�8�C ;fulfillmen-shop-bootstrap/src/main/resources/spy.properties       hB]�
�hB]�
�  v:  ��  �     =u/�~���-2_��ħ�� >fulfillmen-shop-bootstrap/src/main/resources/static/index.html    h0>m-ULh0>m-UL  �
x  ��  �     ��߱���8���vQ��!� <fulfillmen-shop-bootstrap/src/main/resources/static/vite.svg      h0>m1J|h0>m1J|  �
{  ��  �     'e�6>��P���	���.˒�� Gfulfillmen-shop-bootstrap/src/main/resources/templates/import/user.xlsx   h0>m30�h0>m30�  �
}  ��  �     	�<ڷR��"�h��ފ�t�f Jfulfillmen-shop-bootstrap/src/main/resources/templates/mail/activation.ftl        h0>m4h.h0>m4h.  �
~  ��  �     �	yFliv]*8���]�1k���� Ifulfillmen-shop-bootstrap/src/main/resources/templates/mail/captcha-2.ftl haQz0��0haQz0��0  v��  ��  �     
���f�:×�)}�!q��7� Gfulfillmen-shop-bootstrap/src/main/resources/templates/mail/captcha.ftl   h0>m6��h0>m6��  �
�  ��  �     
?�Nњ�]ʦm�� �!��3i Nfulfillmen-shop-bootstrap/src/main/resources/templates/mail/password-reset.ftl    h0>m7�Nh0>m7�N  �
�  ��  �     	�ɳ��X!�������_˓ Yfulfillmen-shop-bootstrap/src/main/resources/templates/mail/registration-confirmation.ftl hy��8^_!hy��8^_!  �
  ��  �     '�t�����Gte:��)��ؘ Ufulfillmen-shop-bootstrap/src/test/java/com/fulfillmen/shop/SimpleThreadPoolTest.java     hy�f�+zhy�f�+z  �  ��  �     ��f��9�q�v�O��e�r�m \fulfillmen-shop-bootstrap/src/test/java/com/fulfillmen/shop/ThreadPoolConfigurationTest.java      hj��ַ�hj��ַ�  ���  ��  �     M�O����?�R�h��6��͋ [fulfillmen-shop-bootstrap/src/test/java/com/fulfillmen/shop/config/GlobalMDCFilterTest.java       hj���s7hj���s7  �̶  ��  �     
�ˁ�VWԎ�T��Z6������� mfulfillmen-shop-bootstrap/src/test/java/com/fulfillmen/shop/config/filter/UnifiedFilterConfigurationTest.java     hy�-�~hy�-�~  �<  ��  �     ֖h���QC%���
� �{��q Afulfillmen-shop-bootstrap/src/test/resources/application-test.yml hy
b'�e<hy
b'�e<  �$  ��  �     �Y�.[L 1�{�㒁�-Rݔ� Lfulfillmen-shop-bootstrap/src/test/resources/application-threadpool-test.yml      h�ʹ7�ʏh�ʹ7�ʏ  "<  ��  �     }�^�τ��#K�e�q�
BL"� fulfillmen-shop-common/pom.xml    hTՆ
�OhTՆ
�O  RI�  ��  �     �.�.�7�����2���w�~�3 Yfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/annotation/RateLimit.java hC�+=�thC�+=�t  "?  ��  �     �=�7e%N
#L��F]�` _fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/annotation/RateLimitIgnore.java   hTՆ
��hTՆ
��  RI�  ��  �     		�SA�k���#�yu4\ \fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/annotation/RateLimiters.java      haP
7�IhaP
7�I  v��  ��  �     I6��T�o�~vC���TM [fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/aspect/RateLimitAspect.java       haP
7�1�haP
7�1�  v��  ��  �     
�|��
�ͻ�I~~w+���  dfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/config/GlobalRateLimitWebConfig.java      hTՆ
��jhTՆ
��j  RJ  ��  �     /�}�)��[x ��Y^�Y
� Vfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/config/I18nConfig.java    h9ן6��`h9ן6��`  vg  ��  �     d����nGR�]q����Sտ�� Yfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/config/JacksonConfig.java haP
7��haP
7��  v��  ��  �     
1<�@�9�
�j
/8�O��� [fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/config/RateLimitConfig.java       h�ϵ&���h�ϵ&���  �  ��  �     G$�?2����%rj�����P� [fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/config/WebClientConfig.java       h����#�h����#�  �6"  ��  �     ���r?�wvs4�u�v�R \fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/context/OrderContextDTO.java      h��'_�h��'_�  ��  ��  �     �+�
LJ�{�j{^��܌�?  Xfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/context/UserContext.java  h��'ʦ(h��'ʦ(  ��  ��  �     )H�JҚ
��7oG�I�-I�Q��W ^fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/context/UserContextHolder.java    hgj�+�hgj�+�  � �  ��  �     	��8Q��4�T�����˪b ]fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/context/UserExtraContext.java     h��V�h��V�  ¤  ��  �     6�U�` ���1�!+���=�� bfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/FulfillmenErrorCodeEnum.java        haR�4���haR�4���  v��  ��  �     .���r\��<m2����\��� gfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/FulfillmenValidationCodeEnum.java   hTՆ
���hTՆ
���  RJ  ��  �     W���:���
c8* ��?�� Wfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/IErrorResult.java   haP
8�haP
8�  v��  ��  �     3z�ŀ��x���&I�
5���� _fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/OpenapiErrorCodeEnum.java   hTՆ.�hTՆ.�  RJ  ��  �     ��Pk���$����ʦh� [fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/RateIntervalUnit.java       hC�+A�hC�+A�  "C  ��  �     ��󌧢�}P�/�iJ�9���� ]fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/RateLimitAlgorithm.java     hC�+B�hC�+B�  "D  ��  �     ϯH�n��C;�	�MQ,]�d [fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/RateLimitKeyType.java       hC�+C��hC�+C��  "E  ��  �     	ƴ�Kng���,� ޤ�9w� _fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/enums/RateLimitStorageType.java   hgj�+�hgj�+�  � �  ��  �     "�~ѣ�x*-+9� �ֻ�9� dfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/exception/BusinessExceptionI18n.java      hTՆBBhTՆBB  RJ  ��  �     ��8��0���G��)ǏX���> cfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/exception/OpenapiExceptionI18n.java       haR�2��haR�2��  v��  ��  �     n��ID`��%A��PtL^,� mfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/exception/handler/GlobalExceptionHandler.java     hTՇ��hTՇ��  RJ�  ��  �     �}����_�Vi_������� tfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/exception/handler/GlobalSaTokenExceptionHandler.java      haP
8�7haP
8�7  v��  ��  �     &��F�fv��_��q�܏}I7 kfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/interceptor/GlobalRateLimitInterceptor.java       haP
8 �whaP
8 �w  v��  ��  �     .U�4�z��uO��7���ݝO� Zfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/model/RateLimitResult.java        h0>mPkh0>mPk  �
�  ��  �      �a��QZ֗�+�s���Qn��6 Wfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/model/package-info.java   h9ן6�E�h9ן6�E�  vo  ��  �     l�t����}�E��1vʉ afulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/properties/CaptchaProperties.java hTՆ;U��hTՆ;U��  RJ�  ��  �     u��[���nC?�܃X� _fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/properties/CommonConstants.java   haP
8#��haP
8#��  v��  ��  �     
x�
~'�6���9���j.�?K@ cfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/properties/RateLimitProperties.java       h9ן6ֈ�h9ן6ֈ�  vq  ��  �     ��i#pq�P*,�{��Q{� ]fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/properties/RsaProperties.java     haP
8*��haP
8*��  v��  ��  �     ���A�N���d�5KF�)��e{x \fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/properties/SysConstants.java      hTՆ {hTՆ {  RJ  ��  �     ��3���ձ�Ī�T����հ� efulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/resolver/CompositeLocaleResolver.java     h0>mW_h0>mW_  �
�  ��  �      �fE�%5a?��;�z�"�jt�} Zfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/sechdule/package-info.java        haP
8.��haP
8.��  v�   ��  �     WS��G�\n&�+}��qf��$	� Zfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/service/TenantService.java        hh�o�@{hh�o�@{  �!	  ��  �     2�Q^�<A�dB*M+pN� afulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/tenant/EnhancedTenantContext.java h��'���h��'���  ��  ��  �     ����[x?�9���e��%�nx� gfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/tenant/EnhancedTenantContextHolder.java   hgj�+��hgj�+��  � �  ��  �     
��2�v���5�7D~�ĳ�ؾ ^fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/tenant/TenantCacheService.java    hgj�+�z�hgj�+�z�  � �  ��  �     !?��
>�d#:T%�4�cas�� bfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/tenant/TenantCacheServiceImpl.java        hgj�, ݝhgj�, ݝ  � �  ��  �     "���&4w
��-�Z�޷ko `fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/tenant/TenantContextExample.java  hj�%���hj�%���  S_  ��  �     
)�T&�|�7[���N�# Xfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/CacheConstants.java  hTՆ"L�hTՆ"L�  RJ  ��  �     MC�#S�I����`�͈��� Xfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/ErrorCodeUtils.java  haP�(�uhaP�(�u  v��  ��  �     -_����݌�η�Z]ty?B Zfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/I18nMessageUtils.java        hTڐ#'�hTڐ#'�  R�}  ��  �     5��
-��h�D�;�	��J�� [fulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/MetaInfoHashUtils.java       haP
88�7haP
88�7  v�  ��  �     8޳E!5�~&��lW��ld��^ Wfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/RateLimitUtil.java   h���%Mh���%M  �O�  ��  �     ��yIJ�`����%�v�9��� Ufulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/SecureUtils.java     haP
8=��haP
8=��  v�  ��  �     dRri.�xr��lV�aFu�� Wfulfillmen-shop-common/src/main/java/com/fulfillmen/shop/common/util/SignatureUtil.java   hC�+PŅhC�+PŅ  "P  ��  �     :�\�3�5<�o
P���� Ifulfillmen-shop-common/src/main/resources/lua/fixed_window_rate_limit.lua hC�+RMJhC�+RMJ  "Q  ��  �     �D��,�h���pq�d�
1� Ifulfillmen-shop-common/src/main/resources/lua/leaky_bucket_rate_limit.lua hC�+S��hC�+S��  "R  ��  �     ද�M��J��SR��� Kfulfillmen-shop-common/src/main/resources/lua/sliding_window_rate_limit.lua       hC�+UG�hC�+UG�  "S  ��  �     [���'Cz�ȹ�_*;'�XS Ifulfillmen-shop-common/src/main/resources/lua/token_bucket_rate_limit.lua haP
8C�,haP
8C�,  v�  ��  �     %��X����ϼz�|� gfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/aspect/MultipleRateLimitAspectTest.java   hTՆ5�NhTՆ5�N  RJ  ��  �     w3��B������40� [fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/config/TestApplication.java       hTՆ7�hTՆ7�  RJ  ��  �     ����U��3�Uâ߼�(H� Vfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/config/TestConfig.java    hTՆ<��hTՆ<��  RJ   ��  �     %�e)�*C*!���>��.�Gǆ xfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/exception/handler/GlobalSaTokenExceptionHandlerTest.java  haP
8L�!haP
8L�!  v�  ��  �     #�.*9� �k�
e�^����� wfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/exception/handler/SimpleGlobalExceptionHandlerTest.java   hTՆ@hTՆ@  RJ"  ��  �     ���d귣��-}��( ɣ� ~fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/exception/handler/SimpleGlobalSaTokenExceptionHandlerTest.java    hTՆD'hTՆD'  RJ%  ��  �     !�'̏�̃;vY5C�&�Z�p� ifulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/resolver/CompositeLocaleResolverTest.java haP
8S�7haP
8S�7  v�  ��  �     %G��-�-_��j��{G>�� \fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/ErrorCodeUtilsTest.java      haQz*�haQz*�  v�  ��  �     !��!�s��i�l}G��W�ܶ�� _fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/I18nDefaultLocaleTest.java   haP�)-�haP�)-�  v��  ��  �     :O 9>j�n�"$^�S����Y ^fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/I18nMessageUtilsTest.java    haP�)	#�haP�)	#�  v��  ��  �     )��G���M��W��z@�ڊ~ ^fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/I18nUsageExampleTest.java    hTՆM�ohTՆM�o  RJ*  ��  �     #�U1K!��x+g��,�P� cfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/JsonBodyNormalizationTest.java       hTՆO��hTՆO��  RJ+  ��  �     %E
�F�2�>'���P
�ަ�� hfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/OpenapiErrorCodeValidationTest.java  haP
8c�VhaP
8c�V  v�  ��  �     �� �F?�:�-Ǩ%�fKN�� cfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/RateLimitUtilCaffeineTest.java       hTՆQZ�hTՆQZ�  RJ,  ��  �     
�J�l�v��+���������� gfulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/SignatureParameterSortingTest.java   hTՇu�3hTՇu�3  RKh  ��  �     =�2���p�v@�P��Ͱ� [fulfillmen-shop-common/src/test/java/com/fulfillmen/shop/common/util/SignatureUtilTest.java       hTՆT�rhTՆT�r  RJ/  ��  �     	��7x9�I�?�kFI8���;�� <fulfillmen-shop-common/src/test/resources/message.properties      haP ��haP ��  v��  ��  �     �wn
��H�6���s~Et\P Bfulfillmen-shop-common/src/test/resources/message_en_US.properties        hTՆW��hTՆW��  RJ1  ��  �     ���E���[koi|�����O Bfulfillmen-shop-common/src/test/resources/message_zh_CN.properties        hTՆY-_hTՆY-_  RJ2  ��  �      �B"dТ�x�O}������=� Dfulfillmen-shop-common/src/test/resources/openapi-message.properties      h9נdfIh9נdfI  we  ��  �     
:g����KP��o��6�M�b fulfillmen-shop-dao/pom.xml       h9נ�h@h9נ�h@  w�  ��  �     �U��*j@�]}+����&�e Zfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/OpenapiAccountMapper.java        h9נ�>Ih9נ�>I  w�  ��  �     �|��T� K�j|�
G dfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/OpenapiAccountPermissionMapper.java      h9נ2�h9נ2�  w�  ��  �     ����/��o�򦦨���T�Ы \fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/OpenapiInterfaceMapper.java      h?���2h?���2  7%  ��  �     �m�����ki�����9~� ]fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/PdcProductMappingMapper.java     h��� ��^h��� ��^  ��  ��  �     ����te��s�'O�>V�� bfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/SysAlibabaCallbackLogsMapper.java        h9ן7�h9ן7�  vv  ��  �     �Ϡ�ߡg��`�*�Q���< ^fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/SysAlibabaCategoryMapper.java    hTՇnhTՇn  RK1  ��  �     阧���c�����'^��ߍ{ bfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantCommissionConfigMapper.java        hh�+�L�hh�+�L�  RK2  ��  �     Ш.�k�|�:eN�'��?�=� Yfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantDomainsMapper.java hTՇA�hTՇA�  RK3  ��  �     �7�T2�ѽ�gN;,Qt�!��� Wfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantFilesMapper.java   hTՇ�jhTՇ�j  RK4  ��  �     ��}��!+���ο~y��}N{u Yfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantLocalesMapper.java hTՇ!�hTՇ!�  RK5  ��  �     ����[Z����ڗ�� ^fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantPlanRelationMapper.java    hTՇ&�:hTՇ&�:  RK6  ��  �     ȍ�{���˝	�%��؊R��� Wfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantPlansMapper.java   haR�W�haR�W�  v�W  ��  �     ԡ���ز{#ů[*�}�d� [fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantWarehouseMapper.java       hTՇ,��hTՇ,��  RK7  ��  �     �8��eq󎊑����]� Wfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantsInfoMapper.java   hTՇ0��hTՇ0��  RK8  ��  �     �I�}�b�sb�+R[�f�u� Sfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TenantsMapper.java       h��' A�h��' A�  ��  ��  �     }�����
�Z�2<*���� Wfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzOrderItemMapper.java   h���
���h���
���  �s$  ��  �     ���~[�<jd� ��{׷ [fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java       h���!gw9h���!gw9  �s�  ��  �     gXj�5��,���_��Yc�" [fulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzOrderSupplierMapper.java       hk.�;Sq�hk.�;Sq�  v��  ��  �     �����9����ڔ1����ز�� Xfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzProductSkuMapper.java  hTՇ�;,hTՇ�;,  RJ�  ��  �     �8f��^�ҦZ���%�� Xfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzProductSpuMapper.java  hTՇ���hTՇ���  RJ�  ��  �     �F|=YČԠ� ��R3Ѻ�g�� Zfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzShoppingCartMapper.java        hu��$b�Vhu��$b�V  ���  ��  �     
��LX�>�a�eJ(�v Yfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzUserAddressMapper.java h9ן7�h9ן7�  vx  ��  �     ��v�6�Jf/|Ú��2
��� Rfulfillmen-shop-dao/src/main/java/com/fulfillmen/shop/dao/mapper/TzUserMapper.java        hAO|!�|;hAO|!�|;  S`  ��  �     ��9�]�2�_W���	�(��Q Ffulfillmen-shop-dao/src/main/resources/mapper/OpenapiAccountMapper.xml    h9נ�3h9נ�3  w�  ��  �     �9��_H9�̭ ´�I׷�& Pfulfillmen-shop-dao/src/main/resources/mapper/OpenapiAccountPermissionMapper.xml  h9נ7�h9נ7�  w�  ��  �     ���=)#)�P�%4%6�c�� Hfulfillmen-shop-dao/src/main/resources/mapper/OpenapiInterfaceMapper.xml  h?���h?���  7&  ��  �     C��W+}�	�0`�o�Yc�H Ifulfillmen-shop-dao/src/main/resources/mapper/PdcProductMappingMapper.xml h��� �h��� �  ��  ��  �     &����T+R��F��<�P�Z Nfulfillmen-shop-dao/src/main/resources/mapper/SysAlibabaCallbackLogsMapper.xml    h9ן7'j�h9ן7'j�  vz  ��  �     �,,��(��3r,[B��� Jfulfillmen-shop-dao/src/main/resources/mapper/SysAlibabaCategoryMapper.xml        hTՇ7hhTՇ7h  RK9  ��  �     TsP� �����O�~��e� Nfulfillmen-shop-dao/src/main/resources/mapper/TenantCommissionConfigMapper.xml    hTՇ>9jhTՇ>9j  RK:  ��  �     ,����Ə�æ�qG�"V���\ Efulfillmen-shop-dao/src/main/resources/mapper/TenantDomainsMapper.xml     hTՇB,%hTՇB,%  RK;  ��  �     ] �):��	��*����x Cfulfillmen-shop-dao/src/main/resources/mapper/TenantFilesMapper.xml       hTՇFEIhTՇFEI  RK<  ��  �     &�|�`J`o����ձ�Q�5�# Efulfillmen-shop-dao/src/main/resources/mapper/TenantLocalesMapper.xml     hTՇL��hTՇL��  RK=  ��  �     sN$�G�u	��9Q�i��V Jfulfillmen-shop-dao/src/main/resources/mapper/TenantPlanRelationMapper.xml        hTՇPhTՇP  RK>  ��  �     x~�4.���^p�9����	�� Cfulfillmen-shop-dao/src/main/resources/mapper/TenantPlansMapper.xml       h��� >h��� >  �  ��  �     
�Y26|���L�A�lB�� Gfulfillmen-shop-dao/src/main/resources/mapper/TenantWarehouseMapper.xml   haPrhaPr  v��  ��  �     ɑ���/,]�����SI��Nd= Cfulfillmen-shop-dao/src/main/resources/mapper/TenantsInfoMapper.xml       hTՇW5�hTՇW5�  RK@  ��  �     ��4�,�?,��j�(�@�� ?fulfillmen-shop-dao/src/main/resources/mapper/TenantsMapper.xml   h����Պh����Պ  ���  ��  �     N��􋌴+(Nn8� F�ֆO Cfulfillmen-shop-dao/src/main/resources/mapper/TzOrderItemMapper.xml       h����[rh����[r  ���  ��  �     �Y�A	1�2⪶�I_�Owb�^ Gfulfillmen-shop-dao/src/main/resources/mapper/TzOrderPurchaseMapper.xml   h���7}h���7}  �s(  ��  �     �#�)^�Zkp������� Gfulfillmen-shop-dao/src/main/resources/mapper/TzOrderSupplierMapper.xml   hk5��Zhk5��Z  v��  ��  �     
�I��*Kњ]�-k�l'nw Dfulfillmen-shop-dao/src/main/resources/mapper/TzProductSkuMapper.xml      h��'
�V�h��'
�V�  �
  ��  �     9_�b�_Zt#�4D�����|� Dfulfillmen-shop-dao/src/main/resources/mapper/TzProductSpuMapper.xml      haQz*���haQz*���  v�  ��  �     	�����I�YP$��☭oV��< Ffulfillmen-shop-dao/src/main/resources/mapper/TzShoppingCartMapper.xml    hu��$d%�hu��$d%�  ���  ��  �     �Kz��L�.��N�g�I+�� Efulfillmen-shop-dao/src/main/resources/mapper/TzUserAddressMapper.xml     hc|,%���hc|,%���  v�  ��  �     
��`���i�s�.������e� >fulfillmen-shop-dao/src/main/resources/mapper/TzUserMapper.xml    haP
8gh{haP
8gh{  v�  ��  �     	�3��z$v:�4U�y�Ga), [fulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/dao/config/MyBatisPlusConfigTest.java       haP
8j+�haP
8j+�  v�
  ��  �     �0��X����6FQ����� Ufulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/dao/config/TestApplication.java     h9ן7��h9ן7��  v�  ��  �     ���Hb���F3���g�J�� Pfulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/dao/config/TestConfig.java  h9ן7�
�h9ן7�
�  v�  ��  �     �H~�E���2ENY�~��� Qfulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/dao/mapper/TTestMapper.java haP
8l��haP
8l��  v�  ��  �     OClj���xje���>�>Aa� Ufulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/dao/mapper/TTestMapperTest.java     haP
8o��haP
8o��  v�  ��  �     �K�.t��܈�W1ꦦk2m�� Vfulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/dao/mapper/TzUserMapperTest.java    haP
8u�|haP
8u�|  v�  ��  �     Џ!�(���)HB7w�X"���c� Cfulfillmen-shop-dao/src/test/java/com/fulfillmen/shop/po/TTest.java       h0>m�9�h0>m�9�  �
�  ��  �     ��:
7�I�`�f�<�-g]�� 6fulfillmen-shop-dao/src/test/resources/application.yml    h0>m�<)h0>m�<)  �
�  ��  �     vKw*;���%���v�=@s{ =fulfillmen-shop-dao/src/test/resources/mapper/TTestMapper.xml     h0>m�A�h0>m�A�  �
�  ��  �     �a�x>:���;�?���G 5fulfillmen-shop-dao/src/test/resources/spy.properties     h9נ�#h9נ�#  w�  ��  �     ߥW�
G�~\�R~w�X�Z\ fulfillmen-shop-domain/pom.xml    haP
8x|haP
8x|  v�  ��  �     ��huB���T��I7�W ffulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/AlibabaCategoryDTOMapping.java    h9ן8x�h9ן8x�  v�  ��  �     ��/��qm�K��PǺ�4 Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/BaseMapping.java  h9ן8�oh9ן8�o  v�  ��  �     ����_�C���q��%%�\i� \fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/BaseTreeMapping.java      h9נfh9נf  w�  ��  �     	\ٶ�v+6�Ї�X+@��C ifulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/OpenapiAccountConvertMapping.java haR���haR���  v��  ��  �     $��ˁ��i��"���_R� efulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/PdcProductConvertMapping.java     h9ן8�h9ן8�  v�  ��  �     �Y���lxP7�!��f�[![� [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/RmbQuotMapping.java       h��'
�Oh��'
�O  �  ��  �     k���]˞e(�Ț�?���� ]fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/TzProductMapping.java     hO�.�P=hO�.�P=  v�  ��  �     �;��Ye�������H5�8<�� bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/TzShoppingCartMapping.java        hu��$e�jhu��$e�j  ���  ��  �     ��\F���M���R>�f�� afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/TzUserAddressMapping.java h9ן8V_h9ן8V_  v�  ��  �     ���d��x I�q�'�CJm��g Zfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/TzUserMapping.java        hgj�,
Mhgj�,
M  � �  ��  �     2���W�o�H�C��l�% lfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/order/AlibabaCreateOrderConvert.java      h��'
��h��'
��  �  ��  �     �{��R#�`�:C[�k���' sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/product/PdcProductDetailConvertMapping.java       haR�8�haR�8�  v�[  ��  �     (��j�ⷛOL���I�/ vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/product/PdcProductRecommendConvertMapping.java    haR���haR���  v�\  ��  �     F�ֹT�/��2�@.��1i�\ sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/convert/product/PdcProductSearchConvertMapping.java       h9ן8!u]h9ן8!u]  v�  ��  �     (G�+�����x3'E�8!��� _fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/AlibabaCategoryTreeDTO.java   haP
8��nhaP
8��n  v�  ��  �     
p�so�0�c�!��Ce�׶�-� Sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/CaptchaDTO.java       haP
8��|haP
8��|  v�  ��  �     䏠/įw���	�n�����F� Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/CaptchaTypeEnum.java  haP
8��haP
8��  v�  ��  �     Y��F��o���gr�EY��� Pfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/PageDTO.java  haS
-]�haS
-]�  v��  ��  �     �k��T$��PlH��s���d Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/ProductInfoDTO.java   hbT).bChbT).bC  x��  ��  �     )0:��ڰ[r�RZ����h؜/p `fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/ProductSearchRequestDTO.java  haP
8���haP
8���  v�  ��  �     �/����l�˜�w ��� [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/RmbQuotResponseDTO.java       h��'
�.}h��'
�.}  �
  ��  �     ~E�';A�L"�>��p�}$\��9 Ufulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/TzProductDTO.java     haQz*���haQz*���  v�!  ��  �     � oy�Ǘ%�F{1����r�,I cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/TzProductSellerDataInfoDTO.java       haP�ØhaP�Ø  v��  ��  �     63������Q�Z�ӟ�� Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/TzProductSkuDTO.java  haP
8�EhaP
8�E  v�  ��  �     �Ie���SǼ9��}R�kUt�� ffulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/openapi/OpenapiAccountInfoDTO.java    hgj�,
�hgj�,
�  � �  ��  �     �#�.�@�tu���D_s��L_ ]fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/order/CreateOrderDTO.java     hgj�,w<hgj�,w<  � �  ��  �     ��e����E���	ߣ5HJF afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/order/CreateOrderRespDTO.java h������h������  �H  ��  �     |E�ݳ�[�a Fb��� Zfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/order/OrderDwdDTO.java        h����^h����^  �6$  ��  �     C��M�c�UH��eG1�o�� \fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/order/OrderItemInfo.java                                ��            �⛲��CK�)�wZ���S� cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/order/UserPurchaseOrderDTO.java       h?��3�h?��3�  7)  ��  �     �S�1n ��#��ܽU�턌 efulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/BaseProductDetailDTO.java     h9נ��h9נ��  wq  ��  �     ��B�A���B$x�
2� �� vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductAttributeCPVDTO.java    h��'D;h��'D;  �  ��  �     X.e�x-!�/Č���z`�`� yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductCertificateListDTO.java h��'��h��'��  �  ��  �     Y��-�
��`�R|��Һ� pfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductDetailDTO.java  haP
8�}-haP
8�}-  v�  ��  �     
��65��y��l4^��1Ye rfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductSaleInfoDTO.java        h?��mUh?��mU  7+  ��  �     ������6��U��]�(` tfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductSellerDataDTO.java      haP
8��yhaP
8��y  v�  ��  �     Q����Ԏ�0��d�~�ۥ�@ vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductShippingInfoDTO.java    haP
8���haP
8���  v�  ��  �     ���F.,�a ɇ�X~�0�C� mfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductSkuDTO.java     h9נ�u�h9נ�u�  wu  ��  �     �� p�����O�)�:��p�" qfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/dto/product/alibaba/AlibabaProductSkuSpecDTO.java h9ן80<h9ן80<  v�  ��  �     �.
�"Z'�����j� `fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/BaseFulfillmenEntity.java  hAO|!ֹ�hAO|!ֹ�  Sc  ��  �     	rxK9{�Rdy'����#� Zfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/OpenapiAccount.java        h9נ:�h9נ:�  w�  ��  �     �+#7N�J�����r�j�#�!= dfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/OpenapiAccountPermission.java      h9נU?h9נU?  w�  ��  �     A�����D@0���)
1�L��) \fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/OpenapiInterface.java      h��'�h��'�  �  ��  �      ��t�
{�h����/��?�� ]fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/PdcProductMapping.java     h��F	��Mh��F	��M  �I  ��  �     
��ͭ���\����C�6̽ bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/SysAlibabaCallbackLogs.java        h9ן87�Yh9ן87�Y  v�  ��  �     �&��`EIY���?��su�q�5 ^fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/SysAlibabaCategory.java    haQz0خhaQz0خ  v��  ��  �     �aYt��8�^tY��M#�� bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantCommissionConfig.java        hTՇfBxhTՇfBx  RKC  ��  �     �xN�D�%�Y��J�ahY�1 Yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantDomains.java hTՇj��hTՇj��  RKD  ��  �     ��O�N�K��a�}�u˧1 Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantFiles.java   hTՇn�hTՇn�  RKE  ��  �     I!۽�|�{�W������\R� Yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantLocales.java hTՇs��hTՇs��  RKF  ��  �     G�
�5jdZ=H����R�	s8 ^fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantPlanRelation.java    hTՇ~�[hTՇ~�[  RKG  ��  �     	�\��ϓ��2=��Ч�RӰbv Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantPlans.java   h��� A�h��� A�  �  ��  �     �3:G��V�
�p���z(�? [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantWarehouse.java       haQz0��[haQz0��[  v��  ��  �     
�r�A��I�t�t��T��( Sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/Tenants.java       haQz0���haQz0���  v��  ��  �     S�q
sB}m�m��9�
��T��+ Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TenantsInfo.java   h���!q�/h���!q�/  �s�  ��  �     쒒G�;9����CP<d�;�L Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzOrderItem.java   h���d�h���d�  �s+  ��  �     !z�/c)ș�f�ݪ�7����� [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzOrderPurchase.java       h��;-��`h��;-��`  �#�  ��  �     5�[��0����ݹw��%'� Yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzOrderRefund.java h��L.���h��L.���  �$/  ��  �      ?iѥ�7%0j6}~�J�:��/ ]fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzOrderRefundItem.java     h�}�7�z�h�}�7�z�  �s�  ��  �     &U��x�Dl��o��� [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzOrderSupplier.java       haP�)Kq�haP�)Kq�  v��  ��  �     nlxy챥_�TP֯]��Q Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzProductSku.java  h��'�$h��'�$  �  ��  �     �A�+1P��X`���'㩶a�- Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzProductSpu.java  haQz.&~haQz.&~  v��  ��  �     눁��{�"�lĔ8&m�M��A Zfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzShipmentItem.java        haQz.)��haQz.)��  v��  ��  �     ��K^3���Ӄ^���x=���[ afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzShipmentProcurement.java haQz0��haQz0��  v��  ��  �     ��sw�˪�]�����i25�  Zfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzShoppingCart.java        h�ގQ�Th�ގQ�T  �  ��  �     8�����Р��� ;�,
� Rfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzUser.java        hu��$rL�hu��$rL�  ���  ��  �     
:2W�i��wo]�V�&�QO3�+: Yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzUserAddress.java h�ALK��h�ALK��  ��  ��  �     �Х���w�$��� 
  cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/TzUserConsumptionRecord.java       h���D�h���D�  �6\  ��  �     �_�"d��5���qF��_%�*� vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/AlibabaCallbackLogsProcessStatusEnum.java    haP
8�nhaP
8�n  v�!  ��  �     ����M!��Z��~��k:��B� cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/EnabledStatusEnum.java       hbR�(�"hbR�(�"  x��  ��  �     4��t�m���{r����q� \fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/GenderEnum.java      hy� Ť�hy� Ť�  ��>  ��  �     ���O��ۮUZ=5�׽b� nfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/OrderSupplierSyncStatusEnums.java    haP
8�٠haP
8�٠  v�#  ��  �     ���� ��*d�c�7f��� qfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/PdcProductMappingSyncStatusEnum.java haP
8�khaP
8�k  v�$  ��  �     ��Di�}Y�r�{p ��[�� bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/PlatformCodeEnum.java        haP
8�N,haP
8�N,  v�%  ��  �     yJi���Z�X�ؕ?6���Hȋ kfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/PlatformProductStatusEnum.java       h�C�
�h�C�
�  ��  ��  �     ��A�Z��W�;1��� mfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/RechargeConsumptionTypeEnum.java     haP
8��haP
8��  v�&  ��  �     ]+��0@�7�R6������ ifulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/ShoppingCartProductType.java hAO|!�U�hAO|!�U�  Sd  ��  �     ��iC��X�o})Π���'��*� cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/SignatureTypeEnum.java       h��o0l�Jh��o0l�J  �K  ��  �     P�F������9E��8 yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/SysAlibabaCallbackLogsProcessStatusEnum.java h���!�6�h�P�!A6,  ��  ��  �     �]f��
KM3�2�;�tN=�� pfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderItemLogisticsStatusEnum.java  hv-�0pC)hv-�0pC)  ��.  ��  �     
���>zٖ�~�s��7>qo�q� gfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderItemStatusEnum.java   h��2l
�h��2l
�  �Y  ��  �     N��"�D��Yh��s��|��- ofulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderPurchaseBuyerTypeEnums.java   h���Fy�h���Fy�  �O�  ��  �     '�9�{xA3�����\�*�Gfk� kfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderPurchaseStatusEnum.java       h���4��Ch���4��C  �(K  ��  �     W�?�� ꢶ�{�@�__���5m lfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderRefundInitiatorEnum.java      h��-5A�h��-5A�  �+  ��  �     E��ӓħh/�.�1��Ƀ qfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderRefundPlatformStatusEnum.java h��1 �۞h��1 �۞  �'�  ��  �     ���
�������&D���~	� ifulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderRefundReasonEnum.java h��3!� �h��3!� �  �)�  ��  �     ��0��B
�O�]h*.4�gz��# ifulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderRefundStatusEnum.java h��j%N�h��j%N�  �')  ��  �     ��9ɒ<N���Qs����� gfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderRefundTypeEnum.java   h��:}�
h��:}�
  �)�  ��  �     {����:§��zm�Ď�l� lfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderRefundWmsStatusEnum.java      hy�-/�hy�-/�  v��  ��  �     ����x�� �㽑���$p sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderSupplierMultipleOrdersEnum.java       h�P� {�&h�P� {�&  �O�  ��  �     ja�t�TS�k3y۩�+��B� kfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzOrderSupplierStatusEnum.java       haP
9 `�haP
9 `�  v�'  ��  �     ��I�����~�_$z�m�wr�� lfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzProductSpuSingleItemEnum.java      haP
9��haP
9��  v�(  ��  �     �+M.�$s�A��W�Z��b� efulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/TzProductStatusEnum.java     haQz0��haQz0��  v��  ��  �     B���=;�#�ߞx�s;O�� `fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/UserStatusEnum.java  hbR�(�JhbR�(�J  x��  ��  �     =�ra)x]\Gn�k��)�U�� ^fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/UserTypeEnum.java    haR��haR��  v�a  ��  �     �z���O�<I<��!�z�K��D cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/WarehouseTypeEnum.java       haR�7haR�7  v�b  ��  �      �N�D�ض�qϊ�񌞎��M ffulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/enums/WmsReceiptStatusEnum.java    haP
9c�haP
9c�  v�*  ��  �     �A��"vN���q��+Ɲ�A|� Yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/AttrJson.java haP
9�-haP
9�-  v�+  ��  �     !@o�% ��rh5�]X���+5� cfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/PdcProductMetaData.java       h9נ$�h9נ$�  w|  ��  �     �W��WH���i��h�a|�c&�r efulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/PdcProductSellerData.java     haP
9&PhaP
9&P  v�,  ��  �     yg��Ujn���F[_�駖45 gfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/PdcProductShippingData.java   haP
9)�YhaP
9)�Y  v�-  ��  �     ��%��c�&၄y�o�2M
 bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/PdcProductSkuData.java        hTՆ;�%�hTՆ;�%�  RJ�  ��  �     �������,�1�
u��7���O ffulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/TzProductSkuSalesInfo.java    hTՆ;���hTՆ;���  RJ�  ��  �     ��M%�l[�/��<#�d��"� efulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/json/TzProductSkuUnitInfo.java     h9ן8yE9h9ן8yE9  v�  ��  �      �B�(5����
��2��o��mi< Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/entity/package-info.java  haP
92TVhaP
92TV  v�.  ��  �     ���r�.���R��QՄ��Yd [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/enums/CurrencyCodeEnum.java       h9ן8}^�h9ן8}^�  v�  ��  �     $�2=/��y~��@=:&f|Բ� \fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/enums/ProductFilterEnum.java      h9ן8��Lh9ן8��L  v�  ��  �     ���Vr��|�� , ��p	� Zfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/enums/ProductSortEnum.java        hTڐ#.��hTڐ#.��  R��  ��  �     ��TO4"�r�XP�>��|� ) bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/enums/ProductSyncStrategyEnum.java        h9ן8�ٺh9ן8�ٺ  v�  ��  �     4��ˋ௃��Z么�(^�m Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/enums/SortOrderEnum.java  hv-�0u�Yhv-�0u�Y  ��2  ��  �     'h�m��H�֩.d��V��J gfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/model/OrderStatusCalculationResult.java   hv-�0xIhv-�0xI  ��3  ��  �     w֕C�ڮҐc��?`��E [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/AggregateSearchReq.java       hTՆ}[hTՆ}[  RJ9  ��  �     �גRw����î4b���/�C* Tfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/BasePageReq.java      h��9h��9  ¥  ��  �     n�Zw�ӆ��c���a���zN� [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/FreightEstimateReq.java       h����Kh����K  ���  ��  �     !�N�����r粬Cw$Wtd
 Qfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/OrderReq.java hTՆ��xhTՆ��x  RJ:  ��  �     ���:,Ł���6(
	��b bfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/SearchProductByKeywordReq.java        hTՆ��ihTՆ��i  RJ;  ��  �     � ��� � 2s+��N�
�?m9 Rfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/SearchReq.java        hTڐ#0�hTڐ#0�  R��  ��  �     
fG����ǻgas�XO!��yk Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/ShoppingCartReq.java  hu��$sk hu��$sk   ���  ��  �     	=�5�_U���9k(��T��@� Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/UserAddressReq.java   hd�*c�{hd�*c�{  �	l  ��  �     ʛ��
��`.���T��� Tfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/UserAuthReq.java      hbR�(���hbR�(���  x��  ��  �     �}�m�+��m���d�6�"�� Vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/req/UserUpdateRep.java    h9ן8��7h9ן8��7  v�  ��  �     ��g�\&��ozI~1^��NoX [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/AlibabaCategoryRes.java       h9ן8�`�h9ן8�`�  v�  ��  �     ��x~�[&x&�,���H�DJ Sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/CaptchaRes.java       haP
9=��haP
9=��  v�1  ��  �     �!�
���$��=}w#p-5�� Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/CurrencyDataRes.java  haP
9C��haP
9C��  v�2  ��  �     ��������+�.�8V�#F� Sfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/RmbQuotRes.java       haP
9G��haP
9G��  v�3  ��  �     ^��n�[C8Y�ȧaQTK� Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/ShoppingCartRes.java  h��M�t�h��M�t�  �   ��  �     d
z	4�߬��Y
(�[�!� [fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/TenantWarehouseRes.java       hu��$tchu��$tc  ���  ��  �     �E�O��FS �g	Vg��u< Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/UserAddressRes.java   haQz0�\haQz0�\  v��  ��  �     ��H+^���0Y#� v/���� Tfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/UserInfoRes.java      haP
9Kz�haP
9Kz�  v�4  ��  �     ����e�do��/�g�{2z� Ufulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/UserLoginRes.java     haP
9U haP
9U   v�5  ��  �     4��>�@5��,j��I�� ]fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/juhe/CurrencyListRes.java     h9ן8��h9ן8��  v�  ��  �     ���bQ{����}1*��� afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/juhe/CurrencyRateInfoRes.java h9ן8�`�h9ן8�`�  v�  ��  �     G����"�E�d{���"!w��(�9 afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/product/ProductDetailRes.java h9ן8��[h9ן8��[  v�  ��  �     N�1����y��T� �w.��g _fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/product/ProductInfoRes.java   h9ן8ùQh9ן8ùQ  v�  ��  �     4��M����	at��1 PU afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/product/ProductInfoV2Res.java h9ן8ȧ
h9ן8ȧ
  v�  ��  �     /G��Pv��0(�e14�2	�c�U ifulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/res/product/SearchProductPageInfoRes.java h��~" W�h��~" W�  �6&  ��  �     8�g�i�BF�"�#l�Z��Ia� afulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/util/CurrencyConversionUtils.java hTՆ��chTՆ��c  RJ?  ��  �     n(�#�mkS�V�j�hm��V� dfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/validation/ValidAggregateSearch.java      haP
9_ɾhaP
9_ɾ  v�7  ��  �     	Pxݸ<������H!�h�׻@� rfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/validation/validator/AggregateSearchValidator.java        h�-9A�h�-9A�  ¦  ��  �     ��i۶�)AAH�밗�"c�JV _fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/FreightEstimateResultVO.java   h���F#h���F#  �s.  ��  �     5��۹���0�Y�6����L Vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/OrderPreviewVO.java    h���Q�h���Q�  �s/  ��  �     ��㒧M��7J��dK�u��@� Ufulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/OrderSubmitVO.java     h��'��h��'��  �  ��  �     ���
j�T`�(�8���
��K�� Wfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/ProductDetailVO.java   h�-8���h�-8���  §  ��  �     ���a'K��SI8��}�wJq�� `fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/ProductFreightEstimateVO.java  h����yh����y  �s1  ��  �     �� Q�~ &R���	6zh�� Ufulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/ProductInfoVO.java     h��� 4<h��� 4<  �s2  ��  �     �q@R�E.���I���� �s� Xfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/SellerDataInfoVO.java  h���!٠h���!٠  �s3  ��  �     
ݒ�#�`88	�=��c
2oP�e� Vfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/ShoppingCartVO.java    h����N�h����N�  �6'  ��  �     ���W2��O�HGi�]���P� _fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/SupplierPreviewResultVO.java   h��� F�h��� F�  �  ��  �     �8���wn�
�ݡ6RFX�]װ� Yfulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/UserOrderDetailVO.java h��C�@�h��C�@�  ���  ��  �     `�S���Il�W����eKz _fulfillmen-shop-domain/src/main/java/com/fulfillmen/shop/domain/vo/UserPurchaseOrderPageVO.java   haP
9d��haP
9d��  v�8  ��  �     /���G?<��&	�7ꭿR afulfillmen-shop-domain/src/test/java/com/fulfillmen/shop/domain/convert/TzProductMappingTest.java hv-�0�gmhv-�0�gm  ��<  ��  �     ��QOϿK[o]rH����f efulfillmen-shop-domain/src/test/java/com/fulfillmen/shop/domain/util/CurrencyConversionUtilsTest.java     h���"��h���"��  �O�  ��  �     z����\i�#���t�"c� fulfillmen-shop-manager/pom.xml   haP
9p��haP
9p��  v�:  ��  �     %�9��r���`�3�	Nqk�j� _fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/config/ProductSyncConfig.java   haRɽ�haRɽ�  v�g  ��  �     I/���O�\��b:��� |�27�� yfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/convert/product/AlibabaProductSearchConvertMapping.java haR��KhaR��K  v�h  ��  �     M�!,ђ�˓wjs��e�F� pfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/convert/product/ProductBaseConvertMapping.java  hgj�,��hgj�,��  � �  ��  �     ߐk.�6	��@��bT�
3�� bfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/common/ICaptchaManager.java        hgj�,'�hgj�,'�  � �  ��  �     ip��%�y:T�k�
�,ᧀ!� ffulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/common/impl/CaptchaManager.java    h������h������  �6(  ��  �     @����0Mޔ	i}a���,( efulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/OrderEventPublisher.java     h��*S��h��*S��  ��  ��  �     ��J|
�����E|��f� pfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CancelPurchaseOrderEvent.java  h��*��Uh��*��U  ��  ��  �     ���_�����D�4����-�Ũ xfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CancelPurchaseOrderEventListener.java  h��P7��h��P7��  �  ��  �     ����!bj7�*��V����Τ� }fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CancelPurchaseOrderSyncAlibabaHandler.java     h���P�&h���P�&  l  ��  �     	�߾B'ܘ�?���4��/�� yfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CancelPurchaseOrderSyncWmsHandler.java h��'5�h��'5�  ��  ��  �     9���9����]C��
�K#� }fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CreatePurchaseOrderSyncAlibabaHandler.java     h����1�h����1�  �6+  ��  �     #>���
^�R�"��A������ yfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CreatePurchaseOrderSyncWmsHandler.java h��+��h��+��  ��e  ��  �     ��J���-)�N��=�!�R�� yfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/CreatedPurchaseOrderEventListener.java hv-�0��hv-�0��  ��A  ��  �     IO�Ŏ��I(_�QB8�8�� rfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/GoodsArrivedWarehouseEvent.java        hv-�0�Fhv-�0�F  ��B  ��  �     y������a�Kb+���7�� ifulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/GoodsShippedEvent.java hv-�0�"hv-�0�"  ��C  ��  �     >�	�w��YܥT��;��i ifulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/GoodsStockedEvent.java hv-�0�5+hv-�0�5+  ��D  ��  �     [�TT�]��M��rM��M�� kfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderCancelledEvent.java       hv-�0�.1hv-�0�.1  ��E  ��  �     �{0�Ӈ��'
1Sw�~�>�� kfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderCompletedEvent.java       h���o%h���o%  ��F  ��  �     �n@f�{l*K���+�R�, ifulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderCreatedEvent.java hv-�0�^mhv-�0�^m  ��G  ��  �     �h���7�ň\l�,���!We�� gfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderEventGroup.java   hv-�0��ihv-�0��i  ��H  ��  �     
��Ҥ��o#5������q�] kfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderEventTypeEnums.java       hv-�0��hv-�0��  ��J  ��  �     ��ikNI;��4X��lZ�["O kfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderExceptionEvent.java       hv-�0�( hv-�0�(   ��K  ��  �     �U��")`*��:�D`GJ� ffulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderPaidEvent.java    h���%Q�+h���%Q�+  �O�  ��  �     _�������!�ٮ�ST`	� nfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderStatusChangeEvent.java    hv-�0�hv-�0�  ��M  ��  �     )��Қ���@�&���aa%i�t� vfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderStatusChangeEventListener.java    hv-�0� �hv-�0� �  ��N  ��  �     
��>=N�-������V��� gfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderStatusFlow.java   hv-�0�
zhv-�0�
z  ��O  ��  �     5�,��ó
�
nr�
�X� lfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderStatusFlowEvent.java      h�� �a�h�� �a�  ��P  ��  �     �=�A��~������|�u rfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/OrderSyncAlibabaOrderEvent.java        h��!#�h��!#�  ��  ��  �     
ö�n��� =�n%o��Ud��d qfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/PaymentPurchaseOrderEvent.java h��P7&�?h��P7&�?  �  ��  �     u����S.�i�(uޒ�-� � fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/PaymentPurchaseOrderEventAlibabaHandler.java   hv-�0�y�hv-�0�y�  ��Q  ��  �     Bg7EcwM�3�p�j�p�{�� qfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/ProcurementCompletedEvent.java hv-�0��}hv-�0��}  ��R  ��  �     �&�ߧ�������ķ��� nfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/ProcurementFailedEvent.java    hv-�0���hv-�0���  ��S  ��  �     �RX�(���V��R�$<S��f ofulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/event/ProcurementStartedEvent.java   h��'"s_h��'"s_  ��  ��  �     v�|� �����=�2�0�F�� kfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/order/helper/OrderContextHelper.java       hgj�,$��hgj�,$��  � �  ��  �     Z���m_�w9�,�����J ofulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/OpenapiAccountRepository.java   h��� ��vh��� ��v  ��  ��  �     Kڛ�E!�Sh�7?��|�-� rfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/PdcProductMappingRepository.java        h��'��/h��'��/  ��  ��  �     ��:��?�n��Fh�� L wfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/SysAlibabaCallbackLogsRepository.java   h���[�bh���[�b  �6_  ��  �     t뫬��ׯ��XVv��:�/ sfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/SysAlibabaCategoryRepository.java       h�}B��`h�}B��`  � �  ��  �     �.�s�,
��4J�����I�` pfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/TzOrderPurchaseRepository.java  hu��6D�hu��6D�  �.�  ��  �     �̆�&N�먝SI��z�\�  mfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/TzProductSkuRepository.java     h���a-h���a-  �6`  ��  �     ��N�Z��A_{�莫6O� mfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/TzProductSpuRepository.java     hgj�,,=�hgj�,,=�  � �  ��  �     Q�=�
��dkC�\�� �1S xfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/OpenapiAccountRepositoryImpl.java  h��� ��h��� ��  ��  ��  �    r#\�y���r���@�Շ�} {fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/PdcProductMappingRepositoryImpl.java       h��'M0h��'M0  ��  ��  �     A���n&�3A����Ŵ" �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/SysAlibabaCallbackLogsRepositoryImpl.java  h���j�h���j�  �6b  ��  �     ���WL
#����\ |fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/SysAlibabaCategoryRepositoryImpl.java      h��P74�h��P74�  �  ��  �     -�N�����9��9�I�AL� yfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/TzOrderPurchaseRepositoryImpl.java hu��6J��hu��6J��  �.�  ��  �     �>1�ѓ��Q���;�4[u�� vfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/TzProductSkuRepositoryImpl.java    h���m[�h���m[�  �6c  ��  �     z�4�d)�Y�Oo��A"��� vfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/impl/TzProductSpuRepositoryImpl.java    hgj�,5��hgj�,5��  � �  ��  �      ��p1���akhBo��%���g)C cfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/core/repository/package-info.java       haP
9tF�haP
9tF�  v�;  ��  �     
8���,�JuƒX�̋?��� afulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/event/ProductDataSyncEvent.java h�����h�����  v�<  ��  �     ��4isI���W���PA.�� efulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/event/ProductSyncEventListener.java     h9ן9Wmh9ן9Wm  v�  ��  �     Kj��P���l�F/�*�� Vfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/event/UserEvent.java    hbR�(�NhbR�(�N  x��  ��  �     Pr������C��V��@�]� Zfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/event/UserEventEnum.java        hgj�,8ۨhgj�,8ۨ  � �  ��  �     	h �yVk;r{,��������� ^fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/event/UserEventListener.java    h0>m�;h0>m�;  �H  ��  �      ���_f9.&���[�F�" Sfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/package-info.java       haP
9���haP
9���  v�A  ��  �      �k�����ߔ����O�xU cfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/platform/IPlatformDataSource.java       haP
9���haP
9���  v�B  ��  �     
�"����p+�텵h�?'_�H`~ ifulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/platform/PlatformDataSourceFactory.java hgj�,;�jhgj�,;�j  � �  ��  �     G���ߤE�����YCS��� jfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/platform/impl/Alibaba1688DataSource.java        h��'��jh��'��j  ��  ��  �     ���S0�_� �c�VO[ bfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/service/IProductSyncService.java        hgj�-�>Ahgj�-�>A  �!  ��  �     -�[�˝�WL#e���|7_]
 _fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/service/TenantDataLoader.java   hj��&��hj��&��  T�z  ��  �     #��j�Z{�E�(�Yˣ���[X$ dfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/service/TenantResolverService.java      h��R���h��R���  �  ��  �     �^�	��f�U(5]��\�C jfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/service/impl/ProductSyncServiceImpl.java        hTڐ#OIThTڐ#OIT  R��  ��  �     C��w/���a�'������ ]fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/strategy/ISyncStrategy.java     hTڐ#PhohTڐ#Pho  R��  ��  �     	0��O4���p�� ��	�~= cfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/strategy/SyncStrategyFactory.java       hgj�,G�hgj�,G�  � �  ��  �     jU��nud

����L#� zt  efulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/strategy/impl/AutoSyncStrategy.java     hTڐ#SehhTڐ#Seh  R��  ��  �     ��Xy����g4Ȼ�j<� ifulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/strategy/impl/DisabledSyncStrategy.java hgj�,Jhgj�,J  � �  ��  �     :��Bn��G9�	�u Ƚp7� gfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/strategy/impl/ManualSyncStrategy.java   hTՆ�JmhTՆ�Jm  RJG  ��  �     ����*{��g)�V��<#� gfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/ICategoryManager.java   h�mc0� �h�mc0� �  v�I  ��  �     	���SK�Ϧ������`�Sj dfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/IOrderManager.java      hv-�8�Xhv-�8�X  ��  ��  �     �����~�X������� bfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/IPayManager.java        h���gh���g  ¨  ��  �     �o+N.�@�R�w'7�0�C�E ffulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/IProductManager.java    hTՆ�T�hTՆ�T�  RJJ  ��  �     k���p��78�-�$�dI dfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/IToolsManager.java      h��*��h��*��  ��  ��  �     ����4)���<\�x��~� xfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/convert/AlibabaOrderDetailConvert.java  haP
9̄haP
9̄  v�K  ��  �     %�`u���*���>��!D��} kfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/impl/CategoryManager.java       h�m��&�h�m��&�  ��X  ��  �     .pg���![��*����=b}GD hfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/impl/OrderManager.java  hvc�*3��hvc�*3��  �(  ��  �     ,,��k��ꃄ_8��XJp��N< ffulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/impl/PayManager.java    h���h���  ©  ��  �     g��5��Q8	�j���b0M.� jfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/impl/ProductManager.java        hTՆ�hTՆ�  RJO  ��  �     
g�M\�6�dɬ�`]ık!� hfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/impl/ToolsManager.java  hTՆ��3hTՆ��3  RJP  ��  �      �v���p����T�)�?�A cfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/package-info.java       haP�)^Q�haP�)^Q�  v��  ��  �     Z����m����w�Ӫ��� nfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/util/OrderErrorCodeUtil.java    h�[�:�z%h�[�:�z%  ƵN  ��  �     �g>���\ɰbDNy�~�h�q cfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/README.md       h���6���h���s��  �6d  ��  �     	'�aP�^9΋�C'V�4]�V;� ifulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/WebhookApi.java h�����h�����  �6/  ��  �     3�����29BS��bD�11�� & zfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/convert/AlibabaOrderConvert.java        h�E(�h�E(�  ƪ@  ��  �     �˿�̩�¯���zЈ���� vfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/event/OrderWebhookEvent.java    h�E(���h�E(���  ƳZ  ��  �     #���r�z���(�iޡ�G�L; ~fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/event/OrderWebhookEventListener.java    h���*�?�h���
x�O  �6e  ��  �     VP?���^����+M� yfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/handler/OrderContextRecord.java h��'��yh��'��y  ��  ��  �     ���Hה��j���>l�q"~ sfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/handler/OrderHandler.java       h��� ���h��� ���  ��  ��  �     ��
��Vz՛��\�u����� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/handler/SyncAlibabaOrderDetailHandler.java      h���4qh���4q  �6h  ��  �     �C,�8,Fj�H�|�Wn
;� |fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/OrderEventProcessor.java      h���Z�h���Z�  �6i  ��  �     
�桁dm= �j)%����#�� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/OrderEventProcessorRegistry.java      h���f&3h���f&3  �6k  ��  �      :�B��3�8!�G#-[� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderCloseProcessor.java h������h������  �6l  ��  �     ���&e�xuh�ig�_O���� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderCompletionProcessor.java    h��� ���h��� ���  ��  ��  �     ІeNd��t-��l�J��� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderConfirmationProcessor.java  h��-���h��-���  �6n  ��  �     ���@%�L�zg�}����]n �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderCreationProcessor.java      h���	� h���	�   �6o  ��  �     �'H���*O�;��Y\�x�� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderPaymentProcessor.java       h����Qh����Q  �6p  ��  �     ��*Յ��n{2g��~���* �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderPriceModificationProcessor.java     h���7V�h���7V�  �6q  ��  �     ��
�82�@�;O)@�%(�(٠ �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderRefundProcessor.java        h��� ��Sh��� ��S  ��  ��  �     �O�E�X{�*��5��u�j �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/processor/impl/OrderShipmentProcessor.java      h��shh��sh  Ƴ�  ��  �     �x������#�7���*� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/service/OrderCompatibilityService.java  h���Y�h���Y�  �61  ��  �     &�ƥ>�u>�j��;��N {fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/service/OrderDataSyncService.java       h��� ǁjh��� ǁj  ��  ��  �     d:�y��n�?��Q��7�Zh��� zfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/service/OrderWebhookService.java        h������h������  �6t  ��  �     H�W_.�?e�m��JNgtGѱ �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderCompatibilityServiceImpl.java h��'�5�h��'�5�  ��  ��  �     w�뷀�0I��i��x�i�T�� �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderDataSyncServiceImpl.java      h��� ��h��� ��  ��  ��  �     5�,�N71L����T$�M$%4 �fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderWebhookServiceImpl.java       haP
9��haP
9��  v�N  ��  �     Pȋ����[
7L��DY�! ffulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/juhe/configure/Currency.java    hTՆ��(hTՆ��(  RJT  ��  �     �qׇ�"�������o5(� jfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/juhe/configure/JuheApiPaths.java        h����	h����	  �6w  ��  �     �����%�GV���)��=�g tfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/juhe/service/ICurrencyExchangeService.java      h������h������  �6x  ��  �     $�#q� ��'eCv:�MD��� |fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/juhe/service/impl/CurrencyExchangeServiceImpl.java      hTՆ��hTՆ��  RJY  ��  �      ��A�|�!{M[w��<+!��, [fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/package-info.java       h���*a�h���*a�  �65  ��  �     !ɀ>�P�=��^R<��j! ^fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/wms/IWmsManager.java    h��'	;Vh��'	;V  ��  ��  �     j-�Q'*hT�3[��[�/�%EKN rfulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/wms/convert/WmsPurchaseOrderConvert.java        h��'�|�h��'�|�  ��  ��  �     N��B���|����y�2�� ffulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/wms/impl/WmsManagerImpl.java    hTՆǂ�hTՆǂ�  RJb  ��  �      ���(=�$=�����X��l _fulfillmen-shop-manager/src/main/java/com/fulfillmen/shop/manager/support/wms/package-info.java   hBWz2�mPhBWz2�mP  
  ��  �     ����������¼Y�9 Cfulfillmen-shop-manager/src/main/resources/application-currency.yml       h0>m*�h0>m*�  �`  ��  �     Jc
	@
���6�-$|�'�� ?fulfillmen-shop-manager/src/main/resources/application-juhe.yml   h9נ�1�h9נ�1�  w�  ��  �     )^���`��4n?�p�mCIK :fulfillmen-shop-manager/src/main/resources/application.yml        hA��(�$�hA��(�$�  v�  ��  �     �*F��%A����kF�uY Xfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/TestConfiguration.java  h9ן9�.�h9ן9�.�  v�  ��  �     ?H^�ٵ+���Y�L������� `fulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/alibaba/config/TestConfig.java  haP
9�ݿhaP
9�ݿ  v�R  ��  �     �,��Io��/?	����g! gfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/alibaba/impl/CategoryManagerTest.java   h�۹ܣh�۹ܣ  �  ��  �     IL�3j��J�i����I	� tfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/core/order/helper/OrderContextHelperPriceTest.java      h��'��h��'��  ��  ��  �     S�lZi�t��M�Ч�%%`� mfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/integration/ProductSyncIntegrationTest.java     hTڐ#Y'hTڐ#Y'  R��  ��  �     	n�&}հ��O3fƊ���' Wfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/integration/README.md   hgj�,Z��hgj�,Z��  � �  ��  �     ;cz�`}{��|���M��C�%J� zfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/repository/impl/PdcProductMappingRepositoryImplTest.java        hgj�,]�~hgj�,]�~  � �  ��  �     <�P��6	+����%}@�Y �fulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/repository/impl/PdcProductMappingRepositoryIntegrationTest.java h��� �9h��� �9  ��  ��  �     Xk���끒At,8�ź��wV�� �fulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/repository/impl/PdcProductMappingRepositorySearchIntegrationTest.java   hgj�,d�hgj�,d�  � �  ��  �     �ʖ]��^��W:��'�}X�h� xfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/repository/impl/SysAlibabaCategoryServiceImplTest.java  hv-�0�A�hv-�0�A�  ��^  ��  �     R���&�aǽ�"V4�"���dr� nfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/service/impl/ProductSyncServiceImplTest.java    hTڐ#` WhTڐ#` W  R��  ��  �     ;�窵��q_W�������> efulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/strategy/MetaInfoHashUtilsTest.java     hgj�,kydhgj�,kyd  � �  ��  �     mG�J�i��S��{�J�: kfulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/strategy/SyncStrategyIntegrationTest.java       h����nAh����nA  �6y  ��  �     ��Ǜ$����AϦm&���~֝ {fulfillmen-shop-manager/src/test/java/com/fulfillmen/shop/manager/support/service/impl/CurrencyExchangeServiceImplTest.java       h�\���h�\���  R��  ��  �     �,y������$����C��G ?fulfillmen-shop-manager/src/test/resources/application-test.yml   hw��36hw��36  ��]  ��  �     �@�X�y�.Ea�(�N
�Qfq� :fulfillmen-shop-manager/src/test/resources/application.yml        h0>m>Dh0>m>D  �y  ��  �     � �r�?�Y�څ3C��h[ Bfulfillmen-shop-manager/src/test/resources/goods/search-goods.json        h0>mm��h0>mm��  �z  ��  �    �Z�T��HJH!C��^Ŏ0j��� >fulfillmen-shop-manager/src/test/resources/goods/充电宝.png    h�9)�Sh�9)�S  �NH  ��  �     �Y�  .�˨6�x��;��4� 6fulfillmen-shop-manager/src/test/resources/orders.json    h�\�)�B4h�\�)�B4  Ƚ�  ��  �     
� �����IU�6UM���+� ?fulfillmen-shop-manager/src/test/resources/run-webhook-tests.sh   h�9a1)eh�9a1)e  �P  ��  �     �5��CV{K��1��VT�� Efulfillmen-shop-manager/src/test/resources/单规格订单-order.json     hTڐ#l֏hTڐ#l֏  R��  ��  �     �I��>�7�Ȭ�@^F\�OW{�� zfulfillmen-shop-system/fulfillmen-shop-admin/src/main/java/com/fulfillmen/shop/admin/controller/ProductSyncController.java        hbR�)�+hbR�)�+  x��  ��  �     35�'{��m�҉ߒ�-�m�M Hfulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/pom.xml  h�uG���h�uG���  �sC  ��  �     Q~`�f}�*xrI�X�ⴔ�i �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/cache/PreviewCacheData.java   h��L *|Qh��L *|Q  �  ��  �     YM��'��4bǳ��c4RV� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/convert/FrontendOrderConvert.java     h��� O�h��� O�  �  ��  �     _��C�6 \3>^��hnU�ޭ� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/convert/FrontendOrderPurchaseConvert.java     h��'!�	h��'!�	  �  ��  �     p�P�w:�]ޅ.O�Dgd"�Β �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/convert/FrontendProductConvert.java   h���vh`h���vh`  �sG  ��  �     QN��{��*��}3-o� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/convert/FrontendShoppingCartConvert.java      haQz0�G�haQz0�G�  v��  ��  �     VV=��bG~�.b�9���W �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/convert/FrontendUserConvert.java      h9ן9��Kh9ן9��K  v�  ��  �      �N�ax��j`-��i��&�c� }fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/package-info.java     h��� R��h��� R��  �  ��  �     ���N��"�;�}c�ߤ�2�, �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/ICommonService.java   h���\�h���\�  ���  ��  �     �jW���oE)kk:����7��J �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/IFrontendOrderService.java    h���~0h���~0  �sI  ��  �     aky�f��{�;1Pl��c�"7 �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/IHomeService.java     h���h���  «  ��  �     .`]y�ԏ<?(7_�� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/IProductService.java  h����!�h����!�  �sK  ��  �     
�o�K����nDwj;4I�� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/IShoppingCartService.java     h�AL���h�AL���  ��  ��  �     D1@�ީ�����ɍ�
�{ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/IUserService.java     h��'N��h��'N��  ��  ��  �     ���+�ر2�
���kAj �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/impl/CommonServiceImpl.java   h��'�	h��'�	  ��  ��  �     �_n��q�ϩ�WE2=;i�:�O� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/impl/FrontendOrderServiceImpl.java    h������h������  �sM  ��  �     ��}�RG�F�a���Q^��\3�R �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/impl/HomeServiceImpl.java     h��'rPh��'rP  ��  ��  �     _��o�@���y!'
��m�5p+ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/impl/ProductServiceImpl.java  h����wrh����wr  �sO  ��  �     u�A>�s�+Xw��,a�`��^ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/impl/ShoppingCartServiceImpl.java     h��'/��h��'/��  ��  ��  �     mÍ���!?T�UגY&���� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/service/impl/UserServiceImpl.java     h���%�W�h���%�W�  �O�  ��  �     'ALh��!��o�`�ԓ��@� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/main/java/com/fulfillmen/shop/frontend/util/OrderStatusUtil.java     hu��$�7�hu��$�7�  ���  ��  �     ר��h��� ҇��I�+q��� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/config/TestApplicationConfig.java     hgj�,���hgj�,���  � �  ��  �     �eX�	�'�2Z�s������ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/config/TestConfig.java        h���-A
h���-A
  ���  ��  �     )��ޝQ$&
'=�k�`�T� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/convert/FrontendOrderPurchaseConvertTest.java h����޵h����޵  �sQ  ��  �     dW�,)+o�����Ƈ���I|�� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/service/impl/ShoppingCartServiceImplTest.java h9ן:�h9ן:�  w  ��  �     �n�o1�B�-\��?W��K_ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/service/impl/UserRegistryServiceDocsExample.java      hgj�,�Vhgj�,�V  � �  ��  �     
�MU
�ͦ�o�Ύ93	�ġ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/service/impl/UserRegistryServiceImplTest.java hTՆ|�hTՆ|�  RJs  ��  �     �UVgz���X�ulp�I�j�# �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/java/com/fulfillmen/shop/frontend/service/impl/UserRegistryServiceIntegrationTest.java  hTՇ M%,hTՇ M%,  RJ�  ��  �     dQң�x+{�R�Z=���>M� hfulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/src/test/resources/application-test.yml  hH��EKhH��EK  w  ��  �     ���9�m� 7ƧL�l��d Dfulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/pom.xml      hv-�."mhv-�."m  ���  ��  �     �Ӷ!�1��3uXP<A���r� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/AddressController.java hw��2_�1hw��2_�1  ��v  ��  �     �=�ɋ��_m�
� �?t
� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/AuthController.java    hTՆ�#hTՆ�#  RJu  ��  �     |����n�LM`E�`�T���1 �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/CaptchaController.java h��� X�h��� X�  �  ��  �     '��7q$in�&Bߚ�pױl�M �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/CommonController.java  h����=�h����=�  �sR  ��  �     \1�۰��2�D�1xT� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/HomeController.java    h��'`��h��'`��  ��  ��  �     G�L̀8#Y�XU#��mR��� �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/OrderController.java   h��&D`�h��&D`�  ­  ��  �     \s���s���N��?�B���5 �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/ProductController.java h������h������  �sU  ��  �     "�dhe��.�x|R& ��Sc �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/ShoppingCartController.java    hu��$���hu��$���  ���  ��  �     )���{�����+�N�m.�G+ �fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/controller/UserController.java    h9ן:Jo�h9ן:Jo�  w  ��  �      ��� H��\���<cQq� yfulfillmen-shop-system/fulfillmen-shop-frontend/frontend-web/src/main/java/com/fulfillmen/shop/frontend/package-info.java h9נ���h9נ���  w�  ��  �     �R:��|,~Bf��v�+���� fulfillmen-shop-system/pom.xml    h0>m��h0>m��  ��  ��  �     �,5�:.DW7���)�d� !kubernetes/docker-deploy-guide.md h0>m��
h0>m��
  ��  ��  �     
�_���*e��&�����?�� kubernetes/helm-chart/README.md   h0>m�cvh0>m�cv  ��  ��  �      ��Y�T?J���K-���>m'�� 0kubernetes/helm-chart/fulfillmen-shop/Chart.yaml  h0>m��qh0>m��q  ��  ��  �     �Jۊ�&�yV�G�ԮZ��I�` <kubernetes/helm-chart/fulfillmen-shop/templates/_helpers.tpl      h0>m���h0>m���  ��  ��  �     �FV���p�|˱��
 >kubernetes/helm-chart/fulfillmen-shop/templates/configmap.yaml    h0>m��fh0>m��f  ��  ��  �     ��{*�m)��5�f�I�M1 ?kubernetes/helm-chart/fulfillmen-shop/templates/deployment.yaml   h0>m��h0>m��  ��  ��  �     zD�`/)��Io��
��ǹ
Y<� <kubernetes/helm-chart/fulfillmen-shop/templates/ingress.yaml      h0>m���h0>m���  ��  ��  �     ���<� �V6 V
ˀf��� ;kubernetes/helm-chart/fulfillmen-shop/templates/secret.yaml       h0>m�yh0>m�y  ��  ��  �     �=
�dph��@����<�i��" <kubernetes/helm-chart/fulfillmen-shop/templates/service.yaml      h0>m��h0>m��  ��  ��  �     F�OH1�(\��^��T�E��� 1kubernetes/helm-chart/fulfillmen-shop/values.yaml hU)�%��hU)�%��  ��  ��  �     ł^	�Eh#�ܼ,H���ǱO kubernetes/kustomize/README.md    hU%
0�/�hU%
0�/�  T�  ��  �     "
��|�bNI��%#�c�#q*� (kubernetes/kustomize/base/configmap.yaml  hU/  `R�hU/  `R�  T�  ��  �     
��_<o<U�+�.�j�C��`� )kubernetes/kustomize/base/deployment.yaml hU�}PhU�}P  T�  ��  �     8�C?�F#8{v(9i�L�h@ &kubernetes/kustomize/base/ingress.yaml    hU$�#2�hU$�#2�  ��  ��  �      ��5\���L�[���.�[���� ,kubernetes/kustomize/base/kustomization.yaml      hU'F!^	UhU'F!^	U  U�  ��  �     n1ow[1�|S�~O �S"7&�M 2kubernetes/kustomize/base/patch/jvm-env-patch.yaml        hU���hU���  T�  ��  �     rԂAn���7'[�"�&9�� %kubernetes/kustomize/base/secret.yaml     hU/,~��hU/,~��  T�  ��  �     `g��������%�?��ƑU &kubernetes/kustomize/base/service.yaml    hU.:,��QhU-��xK  U-v  ��  �     ���'�b���)����>��} Kkubernetes/kustomize/base/后续增强补丁配置/configmap-optimized.yaml       hU.:2�T�hU+75J�`  U,  ��  �     2k9*Q`���?�w�G|w�1�| Nkubernetes/kustomize/base/后续增强补丁配置/deployment-patch-fixes.yaml    hU.;��hU,�0y5�  U7�  ��  �     �6E3�z*��背���n�.�� Ikubernetes/kustomize/base/后续增强补丁配置/high-availability.yaml hU.;��MhU-?�w�  U9
  ��  �     �|�����'��
q�ܔ�@�6 Mkubernetes/kustomize/base/后续增强补丁配置/kustomization-updated.yaml     hU.;�L�hU-'4��  U8�  ��  �     
�ߵ��i:5�1Z��� Bkubernetes/kustomize/base/后续增强补丁配置/monitoring.yaml        hU.;x;�hU,�6�It  U73  ��  �     ��zǿz-$>���r0ԞJ�ǂ Mkubernetes/kustomize/base/后续增强补丁配置/resource-optimization.yaml     hU.;k�hU*�9��q  U+�  ��  �     f�gcw�e��NK��i]].m Jkubernetes/kustomize/base/后续增强补丁配置/secret-credentials.yaml        h0>m�'h0>m�'  ��  ��  �     �%n3�$Z#y�0v,���W 6kubernetes/kustomize/overlays/dev/configmap-patch.yaml    h0>m��uh0>m��u  ��  ��  �     ���g,#z�R����8k� 7kubernetes/kustomize/overlays/dev/deployment-patch.yaml   h0>m���h0>m���  ��  ��  �      �b�D�.b?a�d8���N 4kubernetes/kustomize/overlays/dev/kustomization.yaml      h0>m�+�h0>m�+�  ��  ��  �     )CڐǤ]� >S��mT���d 7kubernetes/kustomize/overlays/prod/configmap-patch.yaml   h0>m�.h0>m�.  ��  ��  �     ��5�� !�X~��e� 8kubernetes/kustomize/overlays/prod/deployment-patch.yaml  h0>m�Գh0>m�Գ  ��  ��  �     �r�Ҽ�Hr��rǭB& /kubernetes/kustomize/overlays/prod/ingress.yaml   h0>m���h0>m���  ��  ��  �      �
|E��P��E��89�~��S 5kubernetes/kustomize/overlays/prod/kustomization.yaml     h���O��h���O��  �6<  ��  �      �c$��.?rg.X\Ԭ�_�٪� :kubernetes/kustomize/overlays/sealos/deployment-patch.yaml        h���S8fh���S8f  �6=  ��  �     IF�li���H�m1�Z/RZq 7kubernetes/kustomize/overlays/sealos/kustomization.yaml   hU/J5��hU-L:�x�  U:.  ��  �     n�Ĉ��A#���J�}�? Bkubernetes/kustomize/overlays/sealos/增强补丁/quick-fixes.yaml        h���WG�h���WG�  �6>  ��  �     ���C*�@�{�SH3�����]� Gkubernetes/kustomize/overlays/sealos/增强补丁/sealos-optimized.yaml   h0>m�ch0>m�c  ��  ��  �     P�81�N�%��Cn)�0�� #kubernetes/native/k8s-configmap.yml       h0>m�h0>m�  ��  ��  �     �'���"M�Z![\�pZ�CL!� #kubernetes/native/k8s-entrypoint.sh       h0>m��h0>m��  ��  ��  �     ��[MvZj����u����G�9 7kubernetes/native/kubernetes-deployment-with-config.yml   h0>m��~h0>m��~  ��  ��  �     	[�����={��q�~�
> +kubernetes/native/kubernetes-deployment.yml       h0>m�h�h0>m�h�  ��  ��  �     I�:��=���ǖJ�T�ulOAv 5kubernetes/springboot3-容器化方案实施文档.md     hk*�
R[�hk*�
R[�  �~�  ��  �      �-g�I��cru¶;�z2� 
lombok.config     h��$.X�_h��$.X�_  �s�  ��  �     S2 ��Ad�'$�s��
�
��� pom.xml   h0>m��Zh0>m��Z  ��  ��  �     �������VtNT3��sr�� 	readme.md TREE  %/ -1 11
.style 4 0
Qx���7r� ���F����docker 1 0
��ڗpsׂG5zXe
Տ�M�kubernetes 41 3
�O�c��>#X���\�C�nnative 4 0
Q}�{�q
�i�`�ykustomize 26 2
�]�Hn�x��P��n�ٕo�base 14 2
Ljܯ�_AE$?��(�z9'��patch 1 0
�X�;/�1���꼧XT�k"后续增强补丁配置 7 0
�v�s�qʃؚ:������overlays 11 3
�ke?�F�4�.o{�}���dev 3 0
*攫�`м3�h(��#=���prod 4 0
�a�:�3�T� �3�t�=�1rsealos 4 1
"�3ɲQZc�q;p.HV\V�增强补丁 2 0
�v���kܗE�� ����i��helm-chart 9 1
��/nɡڱ9ӵ
1�p�U�fulfillmen-shop 8 1
 �m@P{1աm���og�:Ң�templates 6 0
�T�HZ�#�d6�OZ��build-tools 12 2
�_E*��A���S�Y���src 4 2
�t==r�/��$sz%�c,��main 3 1
*�8����i�ߕSǎ��resources 3 1
G�mDs��0H)���j�o��wconfig 3 0
��l3s
������w�XJLtest 1 1
�^�~�r?́�i���FbWOjava 1 1
m�1�x	(����<�0�S���com 1 1
\�{R��$+�&�p��ۃ��fulfillmen 1 1
B��1{�<ͣ��?�j�T2	�shop 1 1
�\���|K�xW�9wSi��test 1 0
�6N�i�L	(� w�-iNZ
��docs 1 0
�z�T.����	���H� �fulfillmen-shop-api 33 2
%l9�]}x���d��W�$��fulfillmen-shop-openapi 18 1
����S��.
��9B�src 15 2
q
2m��/RI-��U^�9main 6 1
䵳���n~��}��Ya<�nvjava 6 1
�3����#��R�F)�ο�com 6 1
m��2k+�U���f *�Y�fulfillmen 6 1
M����3�f
7hdt�?shop 6 1
�c�&r�s��ԜN7��'Y�openapi 6 4
�Z����ş'�Mz�`c�e��context 1 0
�eZ�45̓����3�*��intercept 1 0
���ءޠ����HBn�n�annotation 1 0
m�T)��j� ���MY�A�G�controller 3 0
l�$(9j����M	��3m�Z�test 9 2
�&��ƅ���8�q�+d�}java 6 1
� �����R4�Wc2h�˵��com 6 1
w����b�5��'�?`�X�fulfillmen 6 1
e`<P ��".�W�|��ы=�qshop 6 1
ë%�|�K���E���-�S/openapi 6 2
�����4���3����:�config 1 0
���lzO�*�"�m�������controller 5 0
(�p�U�OY@J\D2mhu�i]resources 3 0
��'�O3�@�^�#:��ڕfulfillmen-shop-openapi-service 15 1
�􉟶!�8h�ʋPlA��_�src 13 1
��I_+�S���F�{7=��main 13 1
����%D�=q�[�˾�c8java 13 1
V��i1�jM��:F�:;d�4com 13 1
RN�B�9�
�oB�����96fulfillmen 13 1
^.��0���A���>¨D�shop 13 1
u�V7+��
s{��Vw���openapi 13 5
ju�W7B���%w\v����vo 5 0
U $���p���R��N��req 1 0
D"�ö�ҷnIb��b��q�0�enums 1 0
�%�I�, 4��g��Nuaconvert 2 0
1!�

A��:�d�� ��service 4 1
����٘�vB���~�\�I�impl 2 0
�T�����'� �Q�p�kfulfillmen-shop-dao 57 1
��
IRff�[0V�V�jݲ�src 56 2
*t�̸'���M��}R`�e�main 46 2
��W���=E�#�)�A��1ޯjava 23 1
ŤH��$�:\�[�?���com 23 1
�:ߖ[�r�=����Ysfulfillmen 23 1
ϸ�}?
D�I`�Y��ʅ/�shop 23 1
S�D�AX#<,n��gŬ�Kdao 23 1
�&�P�T� ���嫯��U�οmapper 23 0
��!�u�6�����	�H�resources 23 1
�_��bn��oB���� 5mapper 23 0
���m���k��g)�x��G	V1test 10 2
�0��},�#q�kM�f
/�java 7 1
6x0�6���+�1�V�G
��!rcom 7 1
J��W��b���И��5�r|�fulfillmen 7 1
Ɖ2#�fw����XBh8��Ishop 7 2
D�*O!L�ƌoU\�_�ܰpo 1 0
F���kl��r9�)��4cdao 6 2
e��Y��uJ����T�S�config 3 0
g�oE�l���$���<M��Qmapper 3 0
Dςx���A%%���8��resources 3 1
`� &��}Ain� |���Apmapper 1 0
�9�%�Υjŝ���y�����fulfillmen-shop-common 73 1
�p�q�]B`�O\9�)@|�src 72 2
D���9<'��ǐo-�W\��main 52 2
��b9v�0I�K���jr@�java 48 1
(߷m�O�|,x0�A�z*�com 48 1
��N�;:QVBf���),�fulfillmen 48 1
@@���\DV�ݿ�M���shop 48 1
��K��z<��?���r Vucommon 48 14
t��·�
��H����LGaeutil 7 0
i��p&�B
�yY�u���enums 8 0
>pAΊ��si��QG%Yf�gmodel 2 0
�����Ȱ~��e	����i�0haspect 1 0
���5Q+��g`x����L�config 5 0
�k�A{�q�� ǜ�I
YZ�)tenant 5 0
�9�.4̹
3 ��lߗ�context 4 0
:i�Ƽ������%7�TD�service 1 0
��EWp'������Z������resolver 1 0
�]x3y2�#돖�Ff�3sechdule 1 0
 ��=���;�A.]Q�����E�exception 4 1
4W��n���z�lz���*-handler 2 0
����õ`��.E��އ0k��annotation 3 0
5�Bl"�$b���"�T�?�properties 5 0
��nυ3�!�̰�;,)�Zainterceptor 1 0
�f΢�̕�+��<�LG�V��&resources 4 1
����܅9�4w��`9��#lua 4 0
����n�>5+�
JLh��test 20 2
������7���$m[�9��O�java 16 1
Xp��BV����@7z�X,D�com 16 1
�]r����@�JeV�ߠ�FPfulfillmen 16 1
?�W��++.��"%�q�㖆shop 16 1
H�cv&#߿a ����TYÏ'_common 16 5
2�ME���j=�|�Y�.q��util 9 0
[��x��=|];¨�٢��aspect 1 0
�O܁�Qư7���YVsw��config 2 0
�;Gmyo2������D�resolver 1 0
�{l�2�^�2�ٶ��exception 3 1
Y��>�[nr"��
�)handler 3 0
��b� dX�Pg� �0�resources 4 0
q�>��<����P�A��d�fulfillmen-shop-domain -1 1
src -1 2
main -1 1
java -1 1
com -1 1
fulfillmen -1 1
shop -1 1
domain -1 10
vo 11 0
����Lm������J����dto -1 3
order -1 0
openapi 1 0
��Kg��A���V߮��product 9 1
�O� �w1{��
�Ё�P���alibaba 8 0
�~O�媅_�Y��saW�req 10 0
	�5� ɡ��(�f��ҝ�{res 15 2
u�@�"����?|���1j���juhe 2 0
U�߫���_�M�:�`���product 4 0
a�J,^�f��凨Lڄ*util 1 0
��(X|S�)gx�\I�7��enums 5 0
-$�`����<=]��n��^model 1 0
�V=R��O�Mj�F���6���entity 66 2
�g:�����v�o�3R����json 7 0
���]�.�K_��R?{�X�enums 29 0
���\�+�$�֫r�����(convert 14 2
{5�1#�+f�n��G����order 1 0
�l �'Gq҆��=v�Y�O�gproduct 3 0
�↮�^��S��%6��'��validation 2 1
�
Ɇ5�B�f�͠�^v�%Fvalidator 1 0
��yW"PÝ��\�#����test 2 1
�7�%@���e�T�(�$? d"java 2 1
Us��XE�*��\I[�2,�=com 2 1
�+\R�J��|G�'���v��:fulfillmen 2 1
jH~���HW7D�w"1=�7Hshop 2 1
�\Y�Qs�S�
zGkl*��domain 2 2
�ޭW����/.Ġ���util 1 0
#�pWZ)�9�c�oі�:Pconvert 1 0
A�jnZ}��k�͕ye���fulfillmen-shop-system 42 2
C����j�I[ߕ�~'�n�fulfillmen-shop-admin 1 1
��P��k�*�_I8��N[src 1 1
d6͟��bx{�P����.main 1 1
�A+"��ܧ<����Jjava 1 1
N�@�($�	�����lE��com 1 1
��h/Z���ͥ����fulfillmen 1 1
�ɱ!���?����u~���shop 1 1
�v.�,�1�RQ����m3JO��admin 1 1
��@r~Q�jW���L:sC1$controller 1 0
���y�}��|/�%ɇiy;fulfillmen-shop-frontend 40 2
:Q20m%L8_���k��?bfrontend-web 11 1
��Ud�ޱ;�ϊz wl�src 10 1
_�\�\� ��:�^A��a�umain 10 1
�`]j�gm�Vz0����$�:aejava 10 1
�"�R����k�-�����bcom 10 1
A'��-�i�L��#���1nS�fulfillmen 10 1
�oTV�ow �xr���y{shop 10 1
��.�=\L��I-��>%�9��frontend 10 1
��$v;��o_"ѻ�lq��cOcontroller 9 0
�[mm���������ae�.frontend-service 29 1
�� �ȥ�OD}a�/�]�A�|�src 28 2
E���?�H�|�]O�ɜk�main 20 1
n|�U9�v|
u���#�lRjava 20 1
t�JE0p�ދ�#�8�6XcZtcom 20 1
�	%�/��M(���I�(��Dfulfillmen 20 1
Z��}�t��/�(��wshop 20 1
h�� 5=7F�JuZ� 9��%frontend 20 4
���a��fh$���z�j��mutil 1 0
Fj���{�������!��cache 1 0
h�<������
dl���convert 5 0
 �E�>G�ma:�tҜ�B�service 12 1
 �����s|�E�Ġr+�8impl 6 0
����]5Zz�����
R��test 8 2
�����5��d�@w·��java 7 1
\�'d.�95��Tkë��Acom 7 1
(~��m$w�PU#�_�fY�fulfillmen 7 1
o���� Y�~�ٗ+��RCshop 7 1
��V_�kQ3�"�:�frontend 7 3
2���ThŖJOc�N�E�config 2 0
��̰.Y ��R��>N�Bconvert 1 0
$y���Ǽ�?-+S��X�4service 4 1
ƴT�
�i qM� v��impl 4 0
�����-�
(C����resources 1 0
ɵ�����]�<���6����fulfillmen-shop-manager 138 1
��ir�eL-(�yr#߇�Ksrc 137 2
�#I�#�~��	���)����main 116 2
����4JNΌT\fM��;java 113 1
�<Q��$!9�*QNYH�!com 113 1
(��@��l��)��j�-fulfillmen 113 1
4�,�4�&���v5��fE�R.shop 113 1
Z�H�,�{�d6�"��N��manager 113 8
������g�-6M��шV9�Ycore 46 3
���<���a�� F�ٓ5order 29 2
_e�/��N ���d����-Vevent 27 0
5��E�c��S	��H�dwd+helper 1 0
�D�oK#��C��S���'Ycommon 2 1
�sO (s�ؕY�impl 1 0
9L>���=r �L�9�t�'ۗrepository 15 1
N	`������X6�����W�impl 7 0
o�z�?�[8�w=�:64�e��event 5 0
����؟zVf�8�6��J��mconfig 1 0
[)^	�h-k=��6�I1ܢ�convert 2 1
bEÝ����g�G4��1��7product 2 0
��:�ͳ��%��a��^�service 4 1
2�-	�D��˰-8�V�impl 1 0
a�mv*
a@����N.LP�:support 46 3

K�yZG;,���&�y��t�wms 4 2
G���vn%��Ba����0impl 1 0
sgŮ�|+�k?���Mx5pYeconvert 1 0
�+,��gA��N����uojuhe 4 2
�3\���%�sx���oL���service 2 1
���O�
$o5�Ĥ�h6��=impl 1 0
/v����#��O���S��,�configure 2 0
{�y`��U$N��@a!0�зalibaba 37 4
�3���=���� ��^lKhimpl 5 0
�`��$*�#���ŭߦ�-util 1 0
?�ZT8�D���#�cÄH![convert 1 0
D{W����\��M��od�v��fwebhook 24 5
�3�Y�s���.z��6M�event 2 0
|`'6���n
��"L�(Y:��convert 1 0
�M�*!��L�W�j�������handler 3 0
J��Jiy!�f�x�	%��?service 6 1
�� \�'8E���T:u1N�vimpl 3 0
��o�FS�d���P��X���processor 10 1

�ǰ��JW�!5�Q�4��,impl 8 0
�Z��U){VI:56������platform 3 1
&{ح�O(Q4�da��eE�impl 1 0
�w� ��[U
�b�9�t�strategy 5 1
?�4�83��S.U59f i��impl 3 0
��/�g��H�l`~����Ѷresources 3 0
��P�`�&^�oG��>�test 21 2
z:�aP����;o��NoQjava 14 1
3dvZj���XM{-h���^D�com 14 1
��j�w8�6�h.�l}�CҋSfulfillmen 14 1
	X�I��Ϙ�3g/Dfߞ��Gshop 14 1
��Yy�40T���ڗ�F�]�manager 14 7
���\�~u���Y�ڍ�core 1 1
�Rl��
���Hu�<[�u�Eorder 1 1
��[�m���� `�� ��helper 1 0
�����O�V
G����alibaba 2 2
J^Η>���<�&������impl 1 0
�X�v�
ƒ	���>c��9�Sconfig 1 0
�Uaj1�dE-��T�^�յ\service 1 1
k�U� �M�tg��U���+{+impl 1 0
��:~ʕ\
k���}ʥ�support 1 1
{���ɮ��!;��A;H�service 1 1
��g̖�|^&��]��ӘB
simpl 1 0
Br�� -�\����
Jv�strategy 2 0
��4�~ ��E�gw���repository 4 1
�V���v
0C5M&9�h���impl 4 0
/����"Vm{?JciT$94�integration 2 0
�3ۘ���q*HZ�M�V8{q��resources 7 1

��&��8;/���s
��M�goods 2 0
����,1����c�H^��(�vfulfillmen-shop-bootstrap 57 2
N��d����﹬| �j����src 52 2
#9ω�{��g���%���	3Emain 46 3
fB��Q������O��wjava 17 1
-15+�?�*�ڣ^�$8icom 17 1
5L
�*�YmOF��i?_ȋdfulfillmen 17 1
�YJŊr:'���d�8`G�shop 17 3
�[.g@����U*Uٙ��4�config 10 2
A��P���I�ܟ��;-5filter 5 0
t7[YQ�[�C}~��+�V�satoken 1 0
\��9A��
�[�,h�l�controller 4 0
b��݄^@?ъ�;v�.n՛secheduler 2 0
��=Xn/���|^�������docker 1 0

V���g\�_���[��0d�resources 28 4
�H�%�~��$��^�db 1 0
�߃0*d����8􆡂�y�i18n 6 0
����$1agQ=Q�\Z�m6^static 2 0
WzD��hA[�T���/�w�templates 6 2
��f���6&��")D�&��9|mail 5 0
�V<�b'��_cf���S!Timport 1 0
�$�3�Ν�}�_O��y�test 6 2
G��l�#F�p4zf���}	Ajava 4 1
�J�A��wߠ>.�.O�c*:�$com 4 1
2�e!!fi�"�$e�79or�fulfillmen 4 1
�F�p`u�$���`C�c#shop 4 1
�ƛ�փ�x)�q����config 2 1
X����Q���u9������filter 1 0
'�_��*��Ȉ�V��h�Y�resources 2 0
�*k(wԊW��Q\_Y����docs 4 0
H�t
�6��>1%y����ʬ���q�(�{0�C(t-�ӻ�O�