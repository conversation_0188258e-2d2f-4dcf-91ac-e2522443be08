/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.domain.req.FreightEstimateReq;
import com.fulfillmen.shop.domain.vo.FreightEstimateResultVO;
import com.fulfillmen.shop.domain.vo.ProductDetailVO;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import com.fulfillmen.shop.frontend.convert.FrontendProductConvert;
import com.fulfillmen.shop.frontend.service.IProductService;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.support.alibaba.api.response.logistics.ProductFreightEstimateResponse;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.time.Duration;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品搜索服务实现
 *
 * <AUTHOR>
 * @date 2025/2/25 19:15
 * @since 1.0.0
 */
@Slf4j
@Service
public class ProductServiceImpl implements IProductService, InitializingBean {

    private final IProductManager productManager;
    private final IProductSyncService productSyncService;
    private final CacheManager cacheManager;
    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSpuMapper tzProductSpuMapper;

    /**
     * Redis缓存 - ProductDetailVO (15分钟)
     */
    private Cache<String, ProductDetailVO> productDetailVOCache;

    public ProductServiceImpl(IProductManager productManager,
      IProductSyncService productSyncService,
      CacheManager cacheManager,
      PdcProductMappingRepository pdcProductMappingRepository, TzProductSpuMapper tzProductSpuMapper) {
        this.productManager = productManager;
        this.productSyncService = productSyncService;
        this.cacheManager = cacheManager;
        this.pdcProductMappingRepository = pdcProductMappingRepository;
        this.tzProductSpuMapper = tzProductSpuMapper;
    }

    /**
     * 上传图片获取图片ID
     *
     * <pre>
     * 1. 文件类型校验 (仅允许图片格式)
     * 2. 文件大小校验 (限制2MB)
     * 3. 图片转Base64编码
     * 4. 调用底层服务上传图片
     * 5. 异常处理逻辑
     * </pre>
     *
     * @param file 图片文件
     * @return 图片ID，用于后续图片搜索
     */
    @Override
    public String uploadImage(MultipartFile file) {
        log.debug("uploadImage request: filename={}, size={}, contentType={}", file.getOriginalFilename(), file
            .getSize(), file.getContentType());
        return productManager.uploadImage(file);
    }

    @Override
    public PageDTO<ProductInfoDTO> searchSimilarProductsSyncDb(Long offerId, Integer pageIndex, Integer pageSize) {
        // 注意：这里保留直接调用Repository，因为相似商品搜索是批量操作，
        // 不适合通过ProductSyncService的单个产品同步逻辑处理
        return pdcProductMappingRepository.searchSimilarProductsSync(offerId, LanguageEnum.EN, pageIndex, pageSize);
    }

    @Override
    public PageDTO<ProductInfoVO> unifiedAggregateSearch(AggregateSearchReq request, Boolean useCache) {
        log.info("聚合搜索VO: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());

        try {
            // 1. 使用 PdcProductMappingRepository 获取聚合搜索结果
            // 注意：聚合搜索是批量操作，保留直接调用Repository
            PageDTO<ProductInfoDTO> searchResult = pdcProductMappingRepository
                .unifiedAggregateSearchWithCache(request, !useCache);

            if (searchResult == null || CollectionUtils.isEmpty(searchResult.getRecords())) {
                log.debug("聚合搜索结果为空: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());
                return PageDTO.<ProductInfoVO>builder()
                    .records(List.of())
                    .pageIndex(request.getPage() != null ? request.getPage() : 1)
                    .pageSize(request.getPageSize() != null ? request.getPageSize() : 10)
                    .total(0L)
                    .build();
            }

            // 3. 转换为前端VO
            PageDTO<ProductInfoVO> voPage = FrontendProductConvert.INSTANCE.toProductInfoVOPage(searchResult);

            log.debug("聚合搜索VO完成，结果数量: {}", voPage.getRecords().size());
            return voPage;

        } catch (Exception e) {
            log.error("聚合搜索VO异常: searchType={}, keyword={}", request.getSearchType(), request.getKeyword(), e);
            return PageDTO.<ProductInfoVO>builder()
                .records(List.of())
                .pageIndex(request.getPage() != null ? request.getPage() : 1)
                .pageSize(request.getPageSize() != null ? request.getPageSize() : 10)
                .total(0L)
                .build();
        }
    }

    /**
     * 🎯 Task 1.4: 清理产品详情缓存
     *
     * @param platformProductId 平台产品ID，null表示清理所有
     * @return 清理结果描述
     */
    @Override
    public String clearProductDetailCache(String platformProductId) {
        try {
            if (platformProductId != null) {
                // 清理指定产品的缓存
                String cacheKey = "productDetail:" + platformProductId;

                productDetailVOCache.remove(cacheKey);

                log.info("已清理产品详情缓存: platformProductId={}", platformProductId);
                return "已清理产品 " + platformProductId + " 的缓存";
            } else {
                // 清理所有缓存 - 注意：这个操作比较重，建议谨慎使用
                log.warn("请求清理所有产品详情缓存");
                return "清理所有缓存功能暂未实现，请指定具体的产品ID";
            }
        } catch (Exception e) {
            log.error("清理产品详情缓存失败: platformProductId={}", platformProductId, e);
            return "清理缓存失败: " + e.getMessage();
        }
    }

    // ==================== 前端VO方法实现 ====================

    @Override
    public ProductDetailVO getProductDetailVO(Long id) {
        log.debug("获取商品详情VO: 传入ID={}", id);
        TzProductDTO productDTO = productSyncService.getOrSyncProductByPlatformId(String.valueOf(id));
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    @Override
    public ProductDetailVO reSyncProductDetailVO(Long id) {
        TzProductDTO productDTO = this.productSyncService.resyncProductByPlatformId(String.valueOf(id), true);
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    /**
     * 估算商品运费
     *
     * @param req 运费估算请求参数
     * @return 运费估算结果
     */
    @Override
    public FreightEstimateResultVO estimateFreight(FreightEstimateReq req) {
        log.info("开始估算商品运费, 请求参数: {}", req);
        try {
            // 1. 判断传入的ID类型并返回实际的平台产品ID
            String platformProductId = determineActualPlatformProductId(req.getOfferId());

            FreightEstimateReq request = FreightEstimateReq.builder()
              .offerId(Long.valueOf(platformProductId))
              .toProvinceCode(req.getToProvinceCode())
              .toCityCode(req.getToCityCode())
              .toCountryCode(req.getToCountryCode())
              .totalNum(req.getTotalNum())
              .logisticsSkuNumModels(req.getLogisticsSkuNumModels())
              .build();

            // 2. 调用Manager层获取1688原始数据
            ProductFreightEstimateResponse.ProductFreightModel freightModel = productManager.estimateFreight(request);

            // 3. 转换为前端VO
            FreightEstimateResultVO vo = convertToFreightEstimateVO(freightModel);
            log.info("商品运费估算成功, offerId: {}, 运费: {}", platformProductId, vo.getFreight());
            return vo;
        } catch (BusinessExceptionI18n e) {
            // 如果是已知的业务异常，直接抛出
            log.warn("商品运费估算业务异常, offerId: {}, 错误: {}", req.getOfferId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            // 对于未知异常，进行包装和记录
            log.error("商品运费估算系统异常, offerId: {}", req.getOfferId(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.FREIGHT_ESTIMATE_FAILED);
        }
    }

    /**
     * 转换1688运费估算结果为前端VO
     *
     * @param freightModel 1688运费模型结果
     * @return 前端运费估算VO
     */
    private FreightEstimateResultVO convertToFreightEstimateVO(ProductFreightEstimateResponse.ProductFreightModel freightModel) {
        if (freightModel == null) {
            log.warn("运费模型为空，返回默认VO");
            return FreightEstimateResultVO.builder()
              .freight("0.00")
              .freePostage(true)
              .build();
        }

        try {
            // 映射到前端需要的字段
            FreightEstimateResultVO vo = FreightEstimateResultVO.builder()
              .freight(freightModel.getFreight())
              .singleProductWeight(freightModel.getSingleProductWeight())
              .chargeType(freightModel.getChargeType())
              .freePostage(freightModel.getFreePostage())
              .sizeValueType(freightModel.getSizeValueType())
              .build();

            log.debug("运费估算VO转换完成: 运费={}, 是否包邮={}, 计费类型={}",
              vo.getFreight(), vo.getFreePostage(), vo.getChargeType());

            return vo;

        } catch (Exception e) {
            log.error("转换运费估算VO时发生异常: freightModel={}", freightModel, e);
            // 返回基础信息，确保前端能正常显示
            return FreightEstimateResultVO.builder()
              .freight(freightModel.getFreight())
              .freePostage(freightModel.getFreePostage())
              .chargeType(freightModel.getChargeType())
              .build();
        }
    }

    /**
     * 判断传入的ID类型并返回实际的平台产品ID
     *
     * @param offerId 传入的offerId（可能是pdcProductMappingId或pdcPlatformProductId）
     * @return 实际的平台产品ID（1688的offerId）
     */
    private String determineActualPlatformProductId(Long offerId) {
        if (offerId == null) {
            throw new IllegalArgumentException("offerId不能为空");
        }

        String offerIdStr = String.valueOf(offerId);

        try {
            // 1. 首先尝试作为pdcProductMappingId查询
            LambdaQueryWrapper<TzProductSpu> pdcMappingQueryWrapper = new LambdaQueryWrapper<>();
            pdcMappingQueryWrapper.eq(TzProductSpu::getIsPdcSync, PdcProductMappingSyncStatusEnum.SYNCED)
              .eq(TzProductSpu::getPdcProductMappingId, offerId);
            TzProductSpu spuByPdcMappingId = tzProductSpuMapper.selectOne(pdcMappingQueryWrapper);
            if (spuByPdcMappingId != null) {
                log.debug("检测到pdcProductMappingId类型: pdcProductMappingId={}, 对应的平台产品ID={}",
                  offerId, spuByPdcMappingId.getPdcPlatformProductId());
                return spuByPdcMappingId.getPdcPlatformProductId();
            }

            // 2. 如果不是pdcProductMappingId，尝试作为pdcPlatformProductId查询
            LambdaQueryWrapper<TzProductSpu> platformQueryWrapper = new LambdaQueryWrapper<>();
            platformQueryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
              .eq(TzProductSpu::getPdcPlatformProductId, offerIdStr);
            TzProductSpu spuByPlatformId = tzProductSpuMapper.selectOne(platformQueryWrapper);

            if (spuByPlatformId != null) {
                log.debug("检测到pdcPlatformProductId类型: platformProductId={}", offerId);
                return offerIdStr;
            }

            // 3. 如果都不是，记录警告并返回原始值
            log.warn("未找到对应的产品记录，offerId: {}, 将直接使用原始值", offerId);
            return offerIdStr;

        } catch (Exception e) {
            log.error("判断ID类型时发生异常, offerId: {}", offerId, e);
            // 发生异常时，返回原始值，避免影响主流程
            return offerIdStr;
        }
    }

    /**
     * 带多级缓存的产品详情获取（重构版 - 已适配简化同步逻辑）
     *
     * <pre>
     * 职责简化：
     * 1. 缓存管理（ProductDetailVO）
     * 2. 数据转换（TzProductDTO -> ProductDetailVO）
     * 3. 通过简化的ProductSyncService统一获取产品数据（3步同步流程）
     * 4. 降级处理（当主流程失败时）
     * </pre>
     */
    private ProductDetailVO getProductDetailVOWithCache(String platformProductId, boolean forceRefresh) {
        String cacheKey = "productDetail:" + platformProductId;

        try {
            // 1. 尝试从ProductDetailVO缓存获取
            if (!forceRefresh) {
                ProductDetailVO cachedVO = productDetailVOCache.get(cacheKey);
                if (cachedVO != null) {
                    log.debug("命中ProductDetailVO缓存: platformProductId={}", platformProductId);
                    return cachedVO;
                }
            }

            // 2. 缓存未命中，通过简化的ProductSyncService获取数据
            log.debug("缓存未命中，通过简化的ProductSyncService获取: platformProductId={}", platformProductId);

            // 使用简化后的统一入口方法
            TzProductDTO productDTO = productSyncService.getOrSyncProductByPlatformId(platformProductId);

            if (productDTO == null) {
                log.error("❌ 无法获取产品信息: platformProductId={}", platformProductId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_NOT_FOUND, platformProductId);
            }

            // 3. 验证产品数据完整性
            validateProductDataIntegrity(productDTO, platformProductId);

            // 4. 转换为前端VO
            ProductDetailVO productDetailVO = convertTzProductDTOToVO(productDTO);

            // 5. 更新多级缓存
            updateCaches(cacheKey, productDTO, productDetailVO, platformProductId);

            log.debug("✅ 商品详情VO转换完成: platformProductId={}, spuId={}", platformProductId, productDTO.getId());
            return productDetailVO;

        } catch (BusinessExceptionI18n e) {
            log.warn("❌ 获取商品详情VO失败: platformProductId={}, 错误码={}, 错误信息={}", platformProductId, e.getErrorCode(), e
                .getMessage());
            throw e;
        } catch (Exception e) {
            log.error("❌ 获取商品详情VO异常: platformProductId={}", platformProductId, e);
            // 最终降级逻辑
            return fallbackToOriginalLogic(Long.valueOf(platformProductId));
        }
    }

    /**
     * 更新多级缓存
     */
    private void updateCaches(String cacheKey,
        TzProductDTO productDTO,
        ProductDetailVO productDetailVO,
        String platformProductId) {
        try {
            productDetailVOCache.put(cacheKey, productDetailVO);
            log.debug("✅ 更新多级缓存完成: platformProductId={}", platformProductId);
        } catch (Exception e) {
            log.warn("⚠️ 更新缓存失败: platformProductId={}", platformProductId, e);
        }
    }

    /**
     * 验证产品数据完整性
     */
    private void validateProductDataIntegrity(TzProductDTO productDTO, String platformProductId) {
        if (productDTO.getId() == null) {
            log.warn("⚠️ 产品ID为空，但继续处理: platformProductId={}", platformProductId);
        }

        if (productDTO.getTitle() == null || productDTO.getTitle().trim().isEmpty()) {
            log.warn("⚠️ 产品标题为空，但继续处理: platformProductId={}", platformProductId);
        }

        if (CollectionUtil.isEmpty(productDTO.getSkuList())) {
            log.warn("⚠️ 产品无SKU信息，但继续处理: platformProductId={}", platformProductId);
        } else {
            log.debug("✅ 产品数据验证通过: platformProductId={}, SKU数量={}", platformProductId, productDTO.getSkuList().size());
        }
    }

    /**
     * 转换 TzProductDTO 为 ProductDetailVO
     */
    private ProductDetailVO convertTzProductDTOToVO(TzProductDTO productDTO) {
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    /**
     * 降级逻辑：通过简化的同步服务获取基础产品数据
     */
    private ProductDetailVO fallbackToOriginalLogic(Long offerId) {
        try {
            log.warn("🔄 降级到简化同步服务: offerId={}", offerId);

            // 通过简化的ProductSyncService再次尝试获取数据
            // 这里使用getAlibabaProductDetail作为最后的降级方案
            AlibabaProductDetailDTO productDetail = productSyncService.getAlibabaProductDetail(String
                .valueOf(offerId), true); // 强制刷新

            if (productDetail == null) {
                log.error("❌ 降级方案也无法找到产品: offerId={}", offerId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_NOT_FOUND, String.valueOf(offerId));
            }

            // 使用转换器进行数据转换
            ProductDetailVO fallbackVO = FrontendProductConvert.INSTANCE.toProductDetailVO(productDetail);

            if (fallbackVO == null) {
                log.error("❌ 数据转换失败: offerId={}", offerId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_DATA_INCOMPLETE, String
                    .valueOf(offerId));
            }

            log.warn("⚠️ 降级逻辑成功，返回基础产品信息: offerId={}, title={}", offerId, fallbackVO.getTitle());
            return fallbackVO;

        } catch (BusinessExceptionI18n e) {
            throw e;
        } catch (Exception e) {
            log.error("❌ 降级方案查询异常: offerId={}", offerId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_SERVICE_DEGRADED, String
                .valueOf(offerId));
        }
    }

    @Override
    public PageDTO<ProductInfoVO> searchSimilarProductsVO(String imagesUrl, Integer pageIndex, Integer pageSize) {
        log.info("搜索相似商品VO: imagesUrl={}, pageIndex={}, pageSize={}", imagesUrl, pageIndex, pageSize);

        // try {
        //     // 1. 通过PdcProductMappingRepository获取相似商品数据
        //     PageDTO<ProductInfoDTO> similarProductsPage = pdcProductMappingRepository
        //         .searchSimilarProductsSync(imagesUrl, LanguageEnum.EN, pageIndex, pageSize);

        //     // 2. 检查结果
        //     if (similarProductsPage == null || CollectionUtils.isEmpty(similarProductsPage.getRecords())) {
        //         log.debug("相似商品搜索结果为空: offerId={}", offerId);
        //         return PageDTO.<ProductInfoVO>builder()
        //             .records(List.of())
        //             .pageIndex(pageIndex != null ? pageIndex : 1)
        //             .pageSize(pageSize != null ? pageSize : 10)
        //             .total(0L)
        //             .build();
        //     }

        //     // 4. 转换为前端VO
        //     PageDTO<ProductInfoVO> voPage = FrontendProductConvert.INSTANCE.toProductInfoVOPage(similarProductsPage);

        //     log.debug("相似商品VO搜索完成，结果数量: {}", voPage.getRecords().size());
        //     return voPage;

        // } catch (Exception e) {
        //     log.error("搜索相似商品VO异常: offerId={}", offerId, e);
        //     return PageDTO.<ProductInfoVO>builder()
        //         .records(List.of())
        //         .pageIndex(pageIndex != null ? pageIndex : 1)
        //         .pageSize(pageSize != null ? pageSize : 10)
        //         .total(0L)
        //         .build();
        // }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 🎯 Phase 1.4: 优化多级缓存配置
        initializeOptimizedCacheConfiguration();
        logCacheConfigurationSummary();
    }

    // ==================== 🎯 Phase 1.4: 优化缓存配置方法 ====================

    /**
     * 🎯 Phase 1.4: 初始化优化的缓存配置
     *
     * <pre>
     * 缓存策略优化：
     * 1. L1缓存(ProductDetailVO): 完整展示数据，中时效，减少转换开销
     * 3. 智能失效策略: 本地+远程双重保障
     * 4. 容量控制: 根据业务特点设置合理容量
     * </pre>
     */
    private void initializeOptimizedCacheConfiguration() {
        // 缓存: ProductDetailVO - 前端展示缓存
        productDetailVOCache = createProductDetailVOCache();

        log.debug("缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]");
    }

    /**
     * 创建ProductDetailVO缓存配置
     *
     * <pre>
     * 优化策略：
     * - 本地缓存8分钟: 前端展示数据较少变化
     * - 远程缓存25分钟: 完整VO对象，转换成本高
     * - 容量300: 支持更多商品详情页访问
     * - 同步机制: 确保多实例数据一致性
     * </pre>
     */
    private Cache<String, ProductDetailVO> createProductDetailVOCache() {
        QuickConfig config = QuickConfig.newBuilder("frontend:product:vo:")
            // 优化: 本地缓存时间调整到8分钟，平衡用户体验和数据一致性
            .localExpire(Duration.ofMinutes(8))
            // 优化: 远程缓存时间调整到25分钟，减少重复转换
            .expire(Duration.ofMinutes(25))
            // 开启空值缓存，防止无效请求重复处理
            .cacheNullValue(true)
            // 双层缓存：本地 + 远程
            .cacheType(CacheType.BOTH)
            // 优化: 本地缓存容量增加到300，支持更多商品详情
            .localLimit(300)
            // 本地缓存同步，保证用户看到一致的商品信息
            .syncLocal(true)
            .build();
        return cacheManager.getOrCreateCache(config);
    }

    /**
     * 记录缓存配置摘要信息
     */
    private void logCacheConfigurationSummary() {
        log.debug("🎯 ProductService优化缓存配置完成:");
        log.debug("  ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:");
        log.debug("  ├── 双层缓存策略: 本地内存 + Redis远程");
        log.debug("  ├── 空值缓存: 已启用，防止缓存穿透");
        log.debug("  └── 同步机制: 已启用，保证多实例一致性");
    }

}
