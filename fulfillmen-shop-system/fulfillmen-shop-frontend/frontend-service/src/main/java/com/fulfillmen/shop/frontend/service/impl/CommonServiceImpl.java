/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.dao.mapper.TenantWarehouseMapper;
import com.fulfillmen.shop.domain.dto.CaptchaDTO;
import com.fulfillmen.shop.domain.dto.RmbQuotResponseDTO;
import com.fulfillmen.shop.domain.entity.TenantWarehouse;
import com.fulfillmen.shop.domain.entity.enums.EnabledStatusEnum;
import com.fulfillmen.shop.domain.res.TenantWarehouseRes;
import com.fulfillmen.shop.frontend.service.ICommonService;
import com.fulfillmen.shop.manager.core.common.ICaptchaManager;
import com.fulfillmen.starter.core.exception.BusinessException;
import jakarta.mail.MessagingException;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 公共服务实现类
 * <pre>
 * 提供验证码服务
 * 提供发送邮箱验证码
 * 提供发送短信验证码
 * 提供通用的服务：
 * - juhe api 服务
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/4/29 17:58
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonServiceImpl implements ICommonService {

    private final ICaptchaManager captchaManager;
    private final TenantWarehouseMapper tenantWarehouseMapper;

    @Override
    public String sendEmailCaptcha(String email) {
        try {
            return captchaManager.sendEmailCaptcha(email);
        } catch (MessagingException e) {
            log.error("Failed to send email captcha to [{}]", email, e);
            throw new BusinessException("Failed to send email captcha: " + e.getMessage());
        }
    }

    @Override
    public CaptchaDTO getCaptchaByImage() {
        return captchaManager.getCaptchaByImage();
    }

    @Override
    public Mono<RmbQuotResponseDTO> getRmbQuot(String type, Integer bank) {
        // TODO: 2025/4/30 待补充开发，底层代码已修改
        return null;
    }

    @Override
    public Mono<RmbQuotResponseDTO> getRmbQuot() {
        // TODO: 2025/4/30 待补充开发，底层代码已修改
        return null;
    }

    @Override
    public List<TenantWarehouseRes> getWarehouseList() {
        log.info("获取租户仓库列表，租户ID: {}", UserContextHolder.getTenantId());

        // 1. 根据当前租户id，查询仓库列表
        LambdaQueryWrapper<TenantWarehouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantWarehouse::getTenantId, UserContextHolder.getTenantId())
          .eq(TenantWarehouse::getWarehouseStatus, EnabledStatusEnum.ENABLED)
          .orderByAsc(TenantWarehouse::getWarehouseSort);

        List<TenantWarehouse> warehouses = tenantWarehouseMapper.selectList(queryWrapper);

        return warehouses.stream()
          .map(this::convertToWarehouseListRes)
          .collect(Collectors.toList());
    }

    private TenantWarehouseRes convertToWarehouseListRes(TenantWarehouse warehouse) {
        TenantWarehouseRes res = new TenantWarehouseRes();
        res.setId(warehouse.getId());
        res.setName(warehouse.getName());
        res.setNameEn(warehouse.getNameEn());
        res.setWarehouseCode(warehouse.getWarehouseCode());
        res.setWarehouseDesc(warehouse.getWarehouseDesc());
        res.setWarehouseIcon(warehouse.getWarehouseIcon());
        res.setWarehouseColor(warehouse.getWarehouseColor());
        res.setWarehouseType(warehouse.getWarehouseType() != null ? warehouse.getWarehouseType().toString() : "1");
        res.setWarehouseSort(warehouse.getWarehouseSort());
        res.setIsDefault(warehouse.getIsDefault());
        res.setCountry(warehouse.getCountry());
        res.setProvince(warehouse.getProvince());
        res.setCity(warehouse.getCity());
        res.setDistrict(warehouse.getDistrict());
        res.setDistrictCode(warehouse.getDistrictCode());
        res.setAddress(warehouse.getAddress());
        res.setPostcode(warehouse.getPostcode());
        res.setLongitude(warehouse.getLongitude());
        res.setLatitude(warehouse.getLatitude());
        res.setContactName(warehouse.getContactName());
        res.setContactPhone(warehouse.getContactPhone());
        res.setContactEmail(warehouse.getContactEmail());
        res.setContactMobile(warehouse.getContactMobile());
        return res;
    }

}
