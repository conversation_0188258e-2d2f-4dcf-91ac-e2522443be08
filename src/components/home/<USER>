<template>
    <div class="promotion-cards">
        <div class="container">
            <div class="rf-cards-scroller">
                <div class="rf-cards-scroller-crop">
                    <div class="rf-cards-scroller-content">
                        <div class="rf-cards-scroller-platter" role="list" :aria-label="t('home.promotions.ariaLabel')">
                            <!-- 黄色卡片 -->
                            <div class="rf-cards-scroller-item" role="listitem">
                                <div class="rf-ccard" @click="navigateToSearch(t('home.promotions.holiday'))">
                                    <div class="rf-ccard-content">
                                        <div class="rf-ccard-img-wrapper">
                                            <img src="@/assets/promotionCard/emma.png" :alt="t('home.promotions.holiday')" class="rf-ccard-img" />
                                        </div>
                                        <div class="rf-ccard-content-info">
                                            <h3 class="rf-ccard-header-eyebrow">{{ t('home.promotions.holiday') }}</h3>
                                            <div class="rf-ccard-influencers">
                                                <span>{{ t('home.promotions.influencers.emma') }}</span>
                                                |
                                                <span>{{ t('home.promotions.influencers.jane') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 儿童游戏区卡片 -->
                            <div class="rf-cards-scroller-item" role="listitem">
                                <div class="rf-ccard" @click="navigateToSearch(t('home.promotions.playtime'))">
                                    <div class="rf-ccard-content">
                                        <div class="rf-ccard-img-wrapper">
                                            <img src="@/assets/promotionCard/childrens-toys.png" :alt="t('home.promotions.playtime')" class="rf-ccard-img" />
                                        </div>
                                        <div class="rf-ccard-content-info">
                                            <h3 class="rf-ccard-header-eyebrow">{{ t('home.promotions.playtime') }}</h3>
                                            <div class="rf-ccard-content-header">
                                                <div>{{ t('home.promotions.playtimeAge') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 礼品套装卡片 -->
                            <div class="rf-cards-scroller-item" role="listitem">
                                <div class="rf-ccard" @click="navigateToSearch(t('home.promotions.giftSets'))">
                                    <div class="rf-ccard-content">
                                        <div class="rf-ccard-img-wrapper">
                                            <img src="@/assets/promotionCard/gift-sets.png" :alt="t('home.promotions.giftSets')" class="rf-ccard-img" />
                                        </div>
                                        <div class="rf-ccard-content-info">
                                            <h3 class="rf-ccard-header-eyebrow">{{ t('home.promotions.giftSets') }}</h3>
                                            <div class="rf-ccard-content-header">
                                                <div>{{ t('home.promotions.giftSetsDesc') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 包装服务卡片 -->
                            <div class="rf-cards-scroller-item" role="listitem">
                                <div class="rf-ccard" @click="navigateToSearch(t('home.promotions.wrapIt'))">
                                    <div class="rf-ccard-content">
                                        <div class="rf-ccard-img-wrapper">
                                            <img src="@/assets/promotionCard/wrap-it-for-less.png" :alt="t('home.promotions.wrapIt')" class="rf-ccard-img" />
                                        </div>
                                        <div class="rf-ccard-content-info">
                                            <h3 class="rf-ccard-header-eyebrow">{{ t('home.promotions.wrapIt') }}</h3>
                                            <div class="rf-ccard-content-header">
                                                <div>{{ t('home.promotions.wrapItDesc') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 时尚礼品卡片 -->
                            <div class="rf-cards-scroller-item" role="listitem">
                                <div class="rf-ccard" @click="navigateToSearch(t('home.promotions.stylishGifts'))">
                                    <div class="rf-ccard-content">
                                        <div class="rf-ccard-img-wrapper">
                                            <img src="@/assets/promotionCard/stylish-gifts.png" :alt="t('home.promotions.stylishGifts')" class="rf-ccard-img" />
                                        </div>
                                        <div class="rf-ccard-content-info">
                                            <h3 class="rf-ccard-header-eyebrow">{{ t('home.promotions.stylishGifts') }}</h3>
                                            <div class="rf-ccard-content-header">
                                                <div>{{ t('home.promotions.wowThem') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 箭头导航 -->
                <div class="paddlenav">
                    <button type="button" class="paddlenav-arrow paddlenav-arrow-previous" :disabled="scrollPosition <= 0" @click="scrollTo('previous')">
                        <span>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36">
                                <path
                                    d="M21.559,12.062 L15.618,17.984 L21.5221,23.944 C22.105,24.533 22.1021,25.482 21.5131,26.065 C21.2211,26.355 20.8391,26.4999987 20.4571,26.4999987 C20.0711,26.4999987 19.6851,26.352 19.3921,26.056 L12.4351,19.034 C11.8531,18.446 11.8551,17.4999987 12.4411,16.916 L19.4411,9.938 C20.0261,9.353 20.9781,9.354 21.5621,9.941 C22.1471,10.528 22.1451,11.478 21.5591,12.062 L21.559,12.062 Z"
                                ></path>
                            </svg>
                        </span>
                        <span class="visuallyhidden">{{ t('common.navigation.previous') }}</span>
                    </button>
                    <button type="button" class="paddlenav-arrow paddlenav-arrow-next" :disabled="scrollPosition >= maxScroll" @click="scrollTo('next')">
                        <span>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36">
                                <path
                                    d="M23.5587,16.916 C24.1447,17.4999987 24.1467,18.446 23.5647,19.034 L16.6077,26.056 C16.3147,26.352 15.9287,26.4999987 15.5427,26.4999987 C15.1607,26.4999987 14.7787,26.355 14.4867,26.065 C13.8977,25.482 13.8947,24.533 14.4777,23.944 L20.3818,17.984 L14.4408,12.062 C13.8548,11.478 13.8528,10.5279 14.4378,9.941 C15.0218,9.354 15.9738,9.353 16.5588,9.938 L23.5588,16.916 L23.5587,16.916 Z"
                                ></path>
                            </svg>
                        </span>
                        <span class="visuallyhidden">{{ t('common.navigation.next') }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    // 引入国际化
    import { onMounted, onUnmounted, ref } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter } from 'vue-router'

    // 获取国际化实例
    const { t } = useI18n()
    // 获取路由实例
    const router = useRouter()

    // 滚动控制变量
    const scrollContent = ref<HTMLElement | null>(null)
    const scrollPosition = ref(0)
    const maxScroll = ref(0)
    const cardWidth = ref(380) // 预设卡片宽度

    // 处理卡片点击，导航到搜索页面
    const navigateToSearch = (keyword: string) => {
        // 导航到搜索页面并传递关键词参数
        router.push({
            name: 'search',
            query: { keyword },
        })
    }

    // 滚动到指定方向
    const scrollTo = (direction: 'previous' | 'next') => {
        if (!scrollContent.value) return

        const container = scrollContent.value
        const currentPosition = container.scrollLeft
        const scrollAmount = cardWidth.value + 24 // 卡片宽度 + 间距

        if (direction === 'previous') {
            container.scrollTo({
                left: Math.max(currentPosition - scrollAmount, 0),
                behavior: 'smooth',
            })
        } else {
            container.scrollTo({
                left: Math.min(currentPosition + scrollAmount, container.scrollWidth - container.clientWidth),
                behavior: 'smooth',
            })
        }
    }

    // 监听滚动事件
    const handleScroll = () => {
        if (!scrollContent.value) return

        const container = scrollContent.value
        scrollPosition.value = container.scrollLeft
        maxScroll.value = container.scrollWidth - container.clientWidth
    }

    // 组件挂载时初始化
    onMounted(() => {
        scrollContent.value = document.querySelector('.rf-cards-scroller-content')

        if (scrollContent.value) {
            scrollContent.value.addEventListener('scroll', handleScroll)
            // 初始化最大滚动距离
            maxScroll.value = scrollContent.value.scrollWidth - scrollContent.value.clientWidth
        }

        // 获取实际卡片宽度
        const cardElement = document.querySelector('.rf-cards-scroller-item')
        if (cardElement) {
            cardWidth.value = cardElement.clientWidth
        }
    })

    // 组件卸载前清理事件监听
    onUnmounted(() => {
        if (scrollContent.value) {
            scrollContent.value.removeEventListener('scroll', handleScroll)
        }
    })
</script>

<style scoped lang="scss">
    .promotion-cards {
        padding: 60px 0;
        background-color: #f8f9fa;

        .container {
            max-width: 2200px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .rf-cards-scroller {
            position: relative;
        }

        .rf-cards-scroller-crop {
            overflow: hidden;
            padding: 20px 0;
        }

        .rf-cards-scroller-content {
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            &::-webkit-scrollbar {
                display: none;
            }
        }

        .rf-cards-scroller-platter {
            display: flex;
            gap: 24px;
            background-color: transparent;
            justify-content: center;
            padding: 0 20px;
            min-width: 2100px; // 确保有足够空间显示所有卡片

            // 移动端左对齐，确保第一张卡片可见
            @media (max-width: 768px) {
                justify-content: flex-start;
                padding: 0 10px;
                min-width: 1700px;
            }
        }

        .rf-cards-scroller-item {
            flex: 0 0 auto;
            width: 380px;
            scroll-snap-align: start;
        }

        .rf-ccard {
            border-radius: 18px;
            overflow: hidden;
            height: 450px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.07);
            transition: all 0.35s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;

            &:hover {
                transform: translateY(-8px);
                box-shadow: 0 12px 28px rgba(0, 0, 0, 0.1);
            }
        }

        .rf-ccard-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .rf-ccard-img-wrapper {
            flex-grow: 1;
            overflow: hidden;
            position: relative;
            background-color: #f0f0f0;

            .rf-ccard-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.4s ease;
            }
        }

        .rf-ccard:hover .rf-ccard-img {
            transform: scale(1.05);
        }

        .rf-ccard-content-info {
            padding: 24px;
            background: #fff;
            position: relative;
            z-index: 2;
            min-height: 140px;
        }

        .rf-ccard-header-eyebrow {
            font-size: 16px;
            font-weight: 700;
            color: #52018d;
            margin-bottom: 10px;
        }

        .rf-ccard-influencers {
            font-size: 14px;
            color: #777;
            margin-bottom: 12px;
        }

        .rf-ccard-content-header {
            font-size: 22px;
            font-weight: 600;
            color: #1d1d1f;
            line-height: 1.3;
        }

        .paddlenav {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            transform: translateY(-50%);
            display: flex;
            justify-content: space-between;
            pointer-events: none;
            padding: 0 10px;
        }

        .paddlenav-arrow {
            pointer-events: auto;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:hover {
                background: #fff;
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }

            &:disabled {
                opacity: 0.3;
                cursor: not-allowed;
                transform: scale(1);
            }

            svg {
                width: 20px;
                height: 20px;
                fill: #1d1d1f;
            }
        }

        .visuallyhidden {
            position: absolute;
            clip: rect(1px, 1px, 1px, 1px);
            clip-path: inset(0px 0px 99.9% 99.9%);
            overflow: hidden;
            height: 1px;
            width: 1px;
            padding: 0;
            border: 0;
        }
    }

    // 响应式调整
    @media (min-width: 2048px) {
        .promotion-cards .container {
            max-width: 2400px;
            padding: 0 50px;
        }

        .promotion-cards .rf-cards-scroller-platter {
            padding: 0 40px;
            min-width: 2300px;
        }

        .promotion-cards .rf-cards-scroller-item {
            width: 420px;
        }

        .promotion-cards .rf-ccard {
            height: 480px;
        }

        .promotion-cards .rf-ccard-content-info {
            padding: 28px;
            min-height: 150px;
        }

        .promotion-cards .rf-ccard-header-eyebrow {
            font-size: 18px;
        }

        .promotion-cards .rf-ccard-content-header {
            font-size: 24px;
        }
    }

    @media (min-width: 2560px) {
        .promotion-cards .container {
            max-width: 2650px;
            padding: 0 60px;
        }

        .promotion-cards .rf-cards-scroller-platter {
            padding: 0 60px;
            min-width: 2520px;
        }

        .promotion-cards .rf-cards-scroller-item {
            width: 460px;
        }

        .promotion-cards .rf-ccard {
            height: 520px;
        }

        .promotion-cards .rf-ccard-content-info {
            padding: 32px;
            min-height: 160px;
        }

        .promotion-cards .rf-ccard-header-eyebrow {
            font-size: 20px;
        }

        .promotion-cards .rf-ccard-content-header {
            font-size: 26px;
        }
    }

    @media (min-width: 3200px) {
        .promotion-cards .container {
            max-width: 2800px;
            padding: 0 80px;
        }

        .promotion-cards .rf-cards-scroller-platter {
            padding: 0 80px;
            min-width: 2740px;
        }

        .promotion-cards .rf-cards-scroller-item {
            width: 500px;
        }

        .promotion-cards .rf-ccard {
            height: 560px;
        }

        .promotion-cards .rf-ccard-content-info {
            padding: 36px;
            min-height: 170px;
        }

        .promotion-cards .rf-ccard-header-eyebrow {
            font-size: 22px;
        }

        .promotion-cards .rf-ccard-content-header {
            font-size: 28px;
        }
    }

    @media (max-width: 768px) {
        .promotion-cards .rf-cards-scroller-item {
            width: 320px;
        }

        .promotion-cards .rf-ccard {
            height: 420px;
        }

        .promotion-cards .paddlenav {
            display: none;
        }
    }
</style>
