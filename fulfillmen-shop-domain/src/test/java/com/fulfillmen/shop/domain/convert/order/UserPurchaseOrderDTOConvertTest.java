/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.convert.order;

import com.fulfillmen.shop.domain.dto.order.UserPurchaseOrderDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.vo.UserPurchaseOrderPageVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserPurchaseOrderDTOConvert 测试类
 *
 * <AUTHOR>
 * @date 2025/8/16
 * @description 测试订单转换器的功能
 */
class UserPurchaseOrderDTOConvertTest {

    @Test
    void testConvertToUserPurchaseOrderDTO() {
        // 准备测试数据
        TzOrderPurchase purchaseOrder = createTestPurchaseOrder();
        List<TzOrderItem> orderItems = createTestOrderItems();

        // 执行转换
        UserPurchaseOrderDTO result = UserPurchaseOrderDTOConvert.INSTANCE
            .convertToUserPurchaseOrderDTO(purchaseOrder, orderItems);

        // 验证基本字段
        assertNotNull(result);
        assertEquals(purchaseOrder.getId(), result.getId());
        assertEquals(purchaseOrder.getPurchaseOrderNo(), result.getPurchaseOrderNo());
        assertEquals(purchaseOrder.getBuyerId(), result.getBuyerId());
        assertEquals(purchaseOrder.getOrderStatus(), result.getOrderStatus());

        // 验证状态展示字段
        assertNotNull(result.getOrderStatusText());
        assertNotNull(result.getOrderStatusDescription());
        assertNotNull(result.getOrderStatusIcon());
        assertNotNull(result.getOrderStatusColor());
        assertNotNull(result.getOrderStatusTagType());
        assertNotNull(result.getProgressPercentage());

        // 验证美元金额字段
        assertNotNull(result.getTotalAmountUsd());
        assertNotNull(result.getShippingFeeUsd());
        assertNotNull(result.getServiceFeeUsd());

        // 验证权限字段
        assertNotNull(result.getCanCancel());
        assertNotNull(result.getCanPay());
        assertNotNull(result.getCanConfirmReceipt());
        assertNotNull(result.getCanApplyRefund());
        assertNotNull(result.getCanViewTracking());

        // 验证主要商品信息
        assertEquals("测试商品标题", result.getMainProductTitle());
        assertEquals("Test Product Title", result.getMainProductTitleEn());
        assertEquals("http://example.com/image.jpg", result.getMainProductImageUrl());

        // 验证订单商品列表
        assertNotNull(result.getOrderItems());
        assertEquals(1, result.getOrderItems().size());

        UserPurchaseOrderDTO.OrderItemInfoDTO itemDto = result.getOrderItems().get(0);
        assertEquals("1", itemDto.getId());
        assertEquals("测试商品标题", itemDto.getProductTitle());
        assertEquals("Test Product Title", itemDto.getProductTitleEn());
        assertEquals(new BigDecimal("100.00"), itemDto.getUnitPrice());
        assertEquals(new BigDecimal("200.00"), itemDto.getLineTotalAmount());
    }

    @Test
    void testConvertToUserPurchaseOrderPageVO() {
        // 准备测试数据
        UserPurchaseOrderDTO dto = createTestUserPurchaseOrderDTO();

        // 执行转换
        UserPurchaseOrderPageVO result = UserPurchaseOrderDTOConvert.INSTANCE
            .convertToUserPurchaseOrderPageVO(dto);

        // 验证转换结果
        assertNotNull(result);
        assertEquals(dto.getPurchaseOrderNo(), result.getOrderNo());
        assertEquals(dto.getPaidDate(), result.getPayTime());
        assertEquals(dto.getCustomerTotalAmount(), result.getTotalAmount());
        assertEquals(dto.getCustomerTotalFreight(), result.getShippingFee());
    }

    private TzOrderPurchase createTestPurchaseOrder() {
        TzOrderPurchase order = new TzOrderPurchase();
        order.setId(1L);
        order.setPurchaseOrderNo("PO202508160001");
        order.setBuyerId(100L);
        order.setOrderStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING);
        order.setOrderDate(LocalDateTime.now());
        order.setPaidDate(LocalDateTime.now());
        order.setServiceFee(new BigDecimal("10.00"));
        order.setExchangeRateSnapshot(new BigDecimal("0.14"));
        order.setCustomerGoodsAmount(new BigDecimal("200.00"));
        order.setCustomerTotalFreight(new BigDecimal("20.00"));
        order.setCustomerTotalAmount(new BigDecimal("230.00"));
        order.setTotalQuantity(2);
        order.setSupplierCount(1);
        order.setLineItemCount(1);
        order.setCompletedSupplierCount(0);
        order.setGmtCreated(LocalDateTime.now());
        order.setGmtModified(LocalDateTime.now());
        return order;
    }

    private List<TzOrderItem> createTestOrderItems() {
        TzOrderItem item = new TzOrderItem();
        item.setId(1L);
        item.setPurchaseOrderId(1L);
        item.setSupplierOrderId(1L);
        item.setProductSpuId(1001L);
        item.setProductSkuId(2001L);
        item.setProductImageUrl("http://example.com/image.jpg");
        item.setProductTitle("测试商品标题");
        item.setProductTitleEn("Test Product Title");
        item.setQuantity(new BigDecimal("2"));
        item.setUnit("件");
        item.setPrice(new BigDecimal("100.00"));
        item.setTotalAmount(new BigDecimal("200.00"));
        return List.of(item);
    }

    private UserPurchaseOrderDTO createTestUserPurchaseOrderDTO() {
        UserPurchaseOrderDTO dto = new UserPurchaseOrderDTO();
        dto.setPurchaseOrderNo("PO202508160001");
        dto.setPaidDate(LocalDateTime.now());
        dto.setCustomerTotalAmount(new BigDecimal("230.00"));
        dto.setCustomerTotalFreight(new BigDecimal("20.00"));
        return dto;
    }
}
