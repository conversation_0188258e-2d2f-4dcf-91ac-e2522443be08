package com.fulfillmen.shop.domain.dto.order;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseBuyerTypeEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/16 10:46
 * @description: todo
 * @since 1.0.0
 */
@Data
public class UserPurchaseOrderDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // 采购订单信息

    /**
     * 采购单ID
     */
    private Long id;

    /**
     * 采购订单编号
     */
    private String purchaseOrderNo;

    /**
     * 买家 ID
     */
    private Long buyerId;

    /**
     * 买家类型： =0 默认系统买家 可以通过 tzuser 获取用户。=1 wms 系统用户，使用的是 cusCode 标识。
     */
    private TzOrderPurchaseBuyerTypeEnums buyerType;

    /**
     * 订单状态: 0待支付/1已支付/2采购中/3待发货/4部分发货/5已发货(所有商品都发货)/6送达仓库(等待入库-质检入库)/7已完成(所有订单已完成)/8已取消
     */
    private TzOrderPurchaseStatusEnum orderStatus;

    /**
     * 下单日期
     */
    private LocalDateTime orderDate;

    /**
     * 支付完成日期
     */
    private LocalDateTime paidDate;

    /**
     * 付费流水号。
     */
    private String paidTransactionNo;

    /**
     * 订单完成日期
     */
    private LocalDateTime orderCompletedDate;

    /**
     * 服务费，整单的服务费价格。通过 tenant_info.service_fee 获取比例。wms 客户需要获取对应的服务费率
     */
    private BigDecimal serviceFee;

    /**
     * 当前交易汇率快照
     * <p>
     * 等于 0 的时候代表订单的汇率是取实时汇率
     * </p>
     */
    private BigDecimal exchangeRateSnapshot;

    /**
     * 客户支付的商品总金额
     */
    private BigDecimal customerGoodsAmount;

    /**
     * 客户需要支付的运费
     */
    private BigDecimal customerTotalFreight;

    /**
     * 订单总金额
     */
    private BigDecimal customerTotalAmount;

    /**
     * 服务费费率
     */
    private BigDecimal serviceFeeRate;

    /**
     * 币种
     */
    private String currency;

    /**
     * 订单商品总数量
     */
    private Integer totalQuantity;

    /**
     * 商品种类数
     */
    private Integer productTypeCount;

    /**
     * 订单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 订单更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 主要商品标题
     */
    private String mainProductTitle;

    /**
     * 主要商品英文标题
     */
    private String mainProductTitleEn;

    /**
     * 主要商品图片
     */
    private String mainProductImageUrl;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 订单商品信息
     */
    private List<OrderItemInfoDTO> orderItems;

    /**
     * 订单商品信息
     */
    @Data
    public static class OrderItemInfoDTO {

        /**
         * 订单项ID
         */
        private String id;

        /**
         * 采购订单ID
         */
        private Long purchaseOrderId;

        /**
         * 供应商订单ID
         */
        private Long supplierOrderId;

        /**
         * 商品ID
         */
        private Long productId;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 商品图片
         */
        private String productImage;

        /**
         * 商品标题
         */
        private String productTitle;

        /**
         * 商品标题(英文)
         */
        private String productTitleEn;

        /**
         * 商品规格
         */
        private List<AttrJson> skuSpecs;

        /**
         * 商品数量
         */
        private Integer orderedQuantity;

        /**
         * 计量单位
         */
        private String unitOfMeasure;

        /**
         * 商品单价
         */
        private BigDecimal unitPrice;

        /**
         * 商品单价(美元)
         */
        private BigDecimal unitPriceUsd;

        /**
         * 行总金额
         */
        private BigDecimal lineTotalAmount;

        /**
         * 行总金额(美元)
         */
        private BigDecimal lineTotalAmountUsd;

        /**
         * 商品状态
         */
        private String itemStatus;

        /**
         * 商品状态描述
         */
        private String itemStatusName;

        /**
         * 商品是否可用
         */
        private Boolean available;

        /**
         * 商品消息
         */
        private String message;
    }
}
