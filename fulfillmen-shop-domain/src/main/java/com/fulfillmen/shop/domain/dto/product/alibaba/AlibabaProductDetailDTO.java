/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.dto.product.alibaba;

import com.fulfillmen.shop.domain.dto.product.BaseProductDetailDTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 商品详情DTO
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AlibabaProductDetailDTO extends BaseProductDetailDTO {

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目名称翻译
     */
    private String categoryNameTrans;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品标题翻译
     */
    private String titleTrans;

    /**
     * 图片列表
     */
    private List<String> images;

    /**
     * 白底图
     */
    private String whiteImage;

    /**
     * 主视频
     */
    private String mainVideo;

    /**
     * 详情视频
     */
    private String detailVideo;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 卖家openId 加密，需要调用解密接口
     */
    private String sellerOpenId;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 商品单位: 件
     */
    private String unit;
    /**
     * 商品单位翻译: Piece
     */
    private String unitTrans;

    /**
     * 商品价格
     *
     * <pre>
     * 如果 priceSaleInfo.quoteType = 0 ，则 以 priceSaleInfo 这个价格为主
     * 如果 priceSaleInfo.quoteType = 1 ，则 以数量区间价格为主
     * 如果 priceSaleInfo.quoteType = 2 ，则 sku 价格为主 (大多数都是看这个)
     * </pre>
     */
    private BigDecimal price;

    /**
     * 商品（美元）价格
     */
    private BigDecimal usdPrice;

    /**
     * 是否为单件商品
     *
     * <pre>
     * 如果<code>true</code>单件商品，则 productSkuList is null
     * 如果<code>false</code> ，则商品SKU不为空，且只有元素，这时候请你将 当前商品定义为一个 sku ，价格请从 productSaleInfo 获取
     * </pre>
     */
    private boolean isSingleItem;

    /**
     * 最小起批量数量
     */
    private Integer minOrderQuantity;

    /**
     * 商品SKU列表
     */
    private List<AlibabaProductSkuDTO> productSkuList;

    /**
     * 商品属性列表
     */
    private List<AlibabaProductAttributeCPVDTO> productAttributeList;

    /**
     * 商品销售信息
     */
    private AlibabaProductSaleInfoDTO productSaleInfo;

    /**
     * 卖家数据信息（从数据库JSON字段解析而来）
     */
    private AlibabaProductSellerDataDTO sellerDataInfo;

    /**
     * 物流信息（从数据库JSON字段解析而来）
     */
    private AlibabaProductShippingInfoDTO shippingInfo;

    /**
     * 证书列表
     */
    private List<AlibabaProductCertificateListDTO> certificateList;
}
