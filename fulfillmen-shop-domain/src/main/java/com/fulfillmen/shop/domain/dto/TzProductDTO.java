/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.dto;

import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 租户商品DTO
 *
 * <AUTHOR>
 * @date 2025/6/16 16:52
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TzProductDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 产品标题
     */
    private String title;

    /**
     * 产品 标题翻译
     */
    private String titleTrans;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品描述翻译
     */
    private String descriptionTrans;

    /**
     * 产品名称 (iPhone 15)
     */
    private String name;

    /**
     * 产品名称翻译
     */
    private String nameTrans;

    /**
     * 类目 id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目名称翻译
     */
    private String categoryNameTrans;

    /**
     * 主图URL
     */
    private String mainImage;

    /**
     * 白底图
     */
    private String whiteImage;

    /**
     * 产品图片列表,json 数组
     */
    private String images;

    /**
     * 单位 中文
     */
    private String unit;

    /**
     * 单位 英文
     */
    private String unitTrans;

    /**
     * 产品主视频
     */
    private String mainVideo;

    /**
     * 产品详情视频
     */
    private String detailVideo;

    /**
     * 商品(Category-Property-Value)CPV 属性，[{}] 。对应商品的属性列表
     */
    private String attributeCpvs;

    /**
     * 商品包裹配送信息相关数据，数据结构不同平台可能不同。{weightwidthheightlength}
     */
    private String shippingInfo;

    /**
     * 商品证书列表
     */
    private String certificateList;

    /**
     * sku件重尺集合。1688 shipping_info字段下，我将它提炼出来。[]
     */
    private String skuShippingDetails;

    /**
     * 最低购买数量
     */
    private Integer minOrderQuantity;

    /**
     * 是否来自 pdc_product_mapping 同步过来的产品。=0 否，=1 是
     */
    private PdcProductMappingSyncStatusEnum isPdcSync;

    /**
     * 是否单品 =0 否、1 是，无 SKU 的产品信息。来源如果不存在 sku 将默认创建一个虚拟的 sku
     */
    private TzProductSpuSingleItemEnum isSingleItem;

    /**
     * pdc_product_mapping.id ,is_pdc_sync=1 ，此值不能为 null
     */
    private Long pdcProductMappingId;

    /**
     * 平台
     */
    private PlatformCodeEnum platformCode;

    /**
     * 平台默认的产品 id ，via: 1688 offerId
     */
    private String pdcPlatformProductId;

    /**
     * 平台默认的产品 id ，via: 1688 卖家 ID
     */
    private String sourcePlatformSellerOpenId;

    /**
     * 平台默认的产品 id ，via: 1688 卖家名称
     */
    private String sourcePlatformSellerName;

    /**
     * 状态: 0 on_shelf(上架)/1 off_shelf(下架)
     */
    private TzProductStatusEnum status;

    /**
     * 上架时间
     */
    private LocalDateTime putawayTime;

    /**
     * 卖家数据信息（从阿里巴巴产品详情中获取的完整卖家信息）
     *
     * <p>
     * DTO应该返回比较齐全的信息，包含完整的卖家评分等详细数据。
     * VO可以限制需要返回的信息都是必要的即可。
     * </p>
     */
    private TzProductSellerDataInfoDTO sellerDataInfo;

    /**
     * sku 列表
     */
    private List<TzProductSkuDTO> skuList;

}
