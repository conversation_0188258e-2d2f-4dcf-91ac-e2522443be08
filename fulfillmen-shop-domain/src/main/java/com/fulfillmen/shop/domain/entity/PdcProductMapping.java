/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformProductStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/3 10:34
 * @description: todo
 * @since 1.0.0
 */

/**
 * 商品映射表 -- 映射: OfferId - ProductId 和 属于平台的元数据 meta_data 和 sku_data 和 stock_data 信息。按需同步策略(按需加载)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "pdc_product_mapping")
public class PdcProductMapping implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 所属平台, 1688 | tb | jd | pdd
     */
    @TableField(value = "platform_code")
    private PlatformCodeEnum platformCode;

    /**
     * 冗余 spu 对应的平台产品 id , 1688 -> OfferId
     */
    @TableField(value = "platform_product_id")
    private String platformProductId;

    /**
     * 一级类目 id
     */
    @TableField(value = "platform_product_top_category_id")
    private String platformProductTopCategoryId;

    /**
     * 二级类目 id
     */
    @TableField(value = "platform_product_second_category_id")
    private String platformProductSecondCategoryId;

    /**
     * 三级类目 id
     */
    @TableField(value = "platform_product_third_category_id")
    private String platformProductThirdCategoryId;

    /**
     * 冗余 spu 对应的平台产品名称
     */
    @TableField(value = "platform_product_name")
    private String platformProductName;

    /**
     * 冗余 spu 对应的平台产品名称 英文
     */
    @TableField(value = "platform_product_name_trans")
    private String platformProductNameTrans;

    /**
     * 冗余 spu 对应的平台产品主图
     */
    @TableField(value = "platform_product_main_image")
    private String platformProductMainImage;

    /**
     * 冗余 spu 对应的平台产品主图
     */
    @TableField(value = "platform_product_white_image")
    private String platformProductWhiteImage;

    /**
     * 复购率
     */
    @TableField(value = "platform_product_repurchase_rate")
    private String platformProductRepurchaseRate;

    /**
     * 商品上架时间
     */
    @TableField(value = "platform_product_create_date")
    private String platformProductCreateDate;

    /**
     * 商品修改时间
     */
    @TableField(value = "platform_product_modify_date")
    private String platformProductModifyDate;

    /**
     * 产品冗余 批发价
     */
    @TableField(value = "platform_product_price")
    private BigDecimal platformProductPrice;

    /**
     * 产品 精选货源价格
     */
    @TableField(value = "platform_product_price_jxhy")
    private BigDecimal platformProductPriceJxhy;

    /**
     * 产品冗余 批发精选货源价格
     */
    @TableField(value = "platform_product_price_pfjxhy")
    private BigDecimal platformProductPricePfjxhy;

    /**
     * 产品冗余一件代发价格
     */
    @TableField(value = "platform_product_price_cosign")
    private BigDecimal platformProductPriceCosign;

    /**
     * 产品冗余 营销价
     */
    @TableField(value = "platform_product_price_promotion")
    private BigDecimal platformProductPricePromotion;

    /**
     * 最低购买量，默认 1
     */
    @TableField(value = "platform_product_min_quantity")
    private Integer platformProductMinQuantity;

    /**
     * 销量
     */
    @TableField(value = "platform_product_sold_out")
    private Integer platformProductSoldOut;

    /**
     * 商品货号
     */
    @TableField(value = "platform_product_cargo_number")
    private String platformProductCargoNumber;

    /**
     * 商品状态。 0 published:上网状态; 1 member expired:会员撤销; 2 auto expired:自然过期; 3 expired:过期(包含手动过期与自动过期); 4 member deleted:会员删除; 5 modified:修改; 6 new:新发; 7 deleted:删除; 8 TBD:to be
     * delete; 9 approved:审批通过; 10 auditing:审核中; 11 untread:审核不通过;
     */
    @TableField(value = "platform_product_status")
    private PlatformProductStatusEnum platformProductStatus;

    /**
     * 是否一件代发 0 否，1 是
     */
    @TableField(value = "platform_product_is_one_psale")
    private Integer platformProductIsOnePsale;

    /**
     * 卖家 SellerOpenId 注意是加密的。 官方说法：不允许除发起旺旺链接以外的 API 使用，系统将它用来针对商品按店铺方式进行整合采购下单。
     */
    @TableField(value = "platform_product_seller_open_id")
    private String platformProductSellerOpenId;

    /**
     * 卖家 SellerOpenId 已解密
     */
    @TableField(value = "platform_product_seller_open_id_decrypt")
    private String platformProductSellerOpenIdDecrypt;

    /**
     * 如果商品被访问过详情页面，默认会同步产品的基本信息。 商品元数据(按需加载)，保存的是各个平台的产品信息。 用于后期使用，数据获取(除了库存信息，其他数据也会相应更新。)
     */
    @TableField(value = "meta_info")
    private String metaInfo;

    /**
     * 产品元数据信息 哈希值 用来比较数据是否发生变化
     */
    @TableField(value = "meta_info_hash")
    private String metaInfoHash;

    /**
     * SKU数据(按需加载)，保存的是各个平台的产品 sku 信息。 用于后期使用，数据获取(除了库存信息，其他数据也会相应更新。)
     */
    @TableField(value = "sku_info")
    private String skuInfo;

    /**
     * 商家属性数据
     */
    @TableField(value = "seller_data_info")
    private String sellerDataInfo;

    /**
     * 商品包裹配送相关数据。针对 1688 数据
     */
    @TableField(value = "shipping_info")
    private String shippingInfo;

    /**
     * 证书列表
     */
    @TableField(exist = false)
    private String certificateList;

    /**
     * 是否同步完成 0 否，1 是。 注意：同步完成，将补充 meta_data 和 sku_data 和 stock_data 信息
     */
    @TableField(value = "is_synced")
    private PdcProductMappingSyncStatusEnum isSynced;

    /**
     * 访问次数
     */
    @TableField(value = "access_count")
    private Integer accessCount;

    /**
     * 搜索结果出现次数
     */
    @TableField(value = "search_count")
    private Integer searchCount;

    /**
     * 详情页查看次数
     */
    @TableField(value = "detail_view_count")
    private Integer detailViewCount;

    /**
     * 交互次数(收藏/加购物车等)
     */
    @TableField(value = "interaction_count")
    private Integer interactionCount;

    /**
     * 首次访问时间
     */
    @TableField(value = "first_access_time")
    private LocalDateTime firstAccessTime;

    /**
     * 最后访问时间
     */
    @TableField(value = "last_access_time")
    private LocalDateTime lastAccessTime;

    /**
     * 数据版本
     */
    @Version
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 是否删除 0 否，1 是
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
