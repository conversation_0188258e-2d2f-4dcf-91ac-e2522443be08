/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: todo
 * @since 1.0.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FreightEstimateResultVO {

    /**
     * 运费
     */
    @Schema(description = "运费")
    private String freight;

    /**
     * 单个产品重量，单位：kg
     */
    @Schema(description = "单个产品重量，单位：kg")
    private Double singleProductWeight;

    /**
     * 计费类型 0: 按重量，1: 按件数 2: 按体积
     */
    @Schema(description = "计费类型 0: 按重量，1: 按件数 2: 按体积")
    private String chargeType;

    /**
     * 是否包邮
     */
    @Schema(description = "是否包邮")
    private Boolean freePostage;

    /**
     * 尺寸值类型 0: 长宽高，1: 长宽，2: 长
     */
    @Schema(description = "尺寸值类型 0: 长宽高，1: 长宽，2: 长")
    private Integer sizeValueType;

}
