package com.fulfillmen.shop.domain.convert.order;

import com.fulfillmen.shop.domain.dto.order.UserPurchaseOrderDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/8/16 11:05
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface UserPurchaseOrderDTOConvert {

    UserPurchaseOrderDTOConvert INSTANCE = Mappers.getMapper(UserPurchaseOrderDTOConvert.class);

    /**
     * 转换采购订单
     * <pre>
     *     1. 采购订单
     *     2. 订单商品
     * </pre>
     *
     * @param purchaseOrder 采购订单
     * @param orderItems    订单商品
     * @return UserPurchaseOrderDTO
     */
    UserPurchaseOrderDTO convertToUserPurchaseOrderDTO(TzOrderPurchase purchaseOrder, List<TzOrderItem> orderItems);
}
