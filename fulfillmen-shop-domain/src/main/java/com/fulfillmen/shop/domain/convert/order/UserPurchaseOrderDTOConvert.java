package com.fulfillmen.shop.domain.convert.order;

import com.fulfillmen.shop.domain.dto.order.UserPurchaseOrderDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.domain.vo.UserPurchaseOrderPageVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/8/16 11:05
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface UserPurchaseOrderDTOConvert {

    UserPurchaseOrderDTOConvert INSTANCE = Mappers.getMapper(UserPurchaseOrderDTOConvert.class);

    /**
     * 转换采购订单为DTO
     * <pre>
     *     1. 采购订单基本信息转换
     *     2. 订单商品信息转换
     *     3. 汇率转换处理
     *     4. 状态展示信息处理
     *     5. 权限判断处理
     * </pre>
     *
     * @param purchaseOrder 采购订单
     * @param orderItems    订单商品
     * @return UserPurchaseOrderDTO
     */
    @Mappings({
        @Mapping(target = "id", source = "purchaseOrder.id"),
        @Mapping(target = "purchaseOrderNo", source = "purchaseOrder.purchaseOrderNo"),
        @Mapping(target = "buyerId", source = "purchaseOrder.buyerId"),
        @Mapping(target = "buyerType", source = "purchaseOrder.buyerType"),
        @Mapping(target = "orderStatus", source = "purchaseOrder.orderStatus"),
        @Mapping(target = "orderDate", source = "purchaseOrder.orderDate"),
        @Mapping(target = "paidDate", source = "purchaseOrder.paidDate"),
        @Mapping(target = "paidTransactionNo", source = "purchaseOrder.paidTransactionNo"),
        @Mapping(target = "orderCompletedDate", source = "purchaseOrder.orderCompletedDate"),
        @Mapping(target = "serviceFee", source = "purchaseOrder.serviceFee"),
        @Mapping(target = "exchangeRateSnapshot", source = "purchaseOrder.exchangeRateSnapshot"),
        @Mapping(target = "customerGoodsAmount", source = "purchaseOrder.customerGoodsAmount"),
        @Mapping(target = "customerTotalFreight", source = "purchaseOrder.customerTotalFreight"),
        @Mapping(target = "customerTotalAmount", source = "purchaseOrder.customerTotalAmount"),
        @Mapping(target = "serviceFeeRate", expression = "java(getServiceFeeRate())"),
        @Mapping(target = "currency", expression = "java(getCurrency())"),
        @Mapping(target = "totalQuantity", source = "purchaseOrder.totalQuantity"),
        @Mapping(target = "productTypeCount", expression = "java(orderItems != null ? orderItems.size() : 0)"),
        @Mapping(target = "createTime", source = "purchaseOrder.gmtCreated"),
        @Mapping(target = "updateTime", source = "purchaseOrder.gmtModified"),
        // 状态展示字段
        @Mapping(target = "orderStatusText", expression = "java(getOrderStatusText(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "orderStatusDescription", expression = "java(getOrderStatusDescription(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "orderStatusIcon", expression = "java(getOrderStatusIcon(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "orderStatusColor", expression = "java(getOrderStatusColor(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "orderStatusTagType", expression = "java(getOrderStatusTagType(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "progressPercentage", expression = "java(getProgressPercentage(purchaseOrder.getOrderStatus()))"),
        // 美元金额字段
        @Mapping(target = "totalAmountUsd", expression = "java(convertToUsd(purchaseOrder.getCustomerTotalAmount(), purchaseOrder.getExchangeRateSnapshot()))"),
        @Mapping(target = "shippingFeeUsd", expression = "java(convertToUsd(purchaseOrder.getCustomerTotalFreight(), purchaseOrder.getExchangeRateSnapshot()))"),
        @Mapping(target = "serviceFeeUsd", expression = "java(convertToUsd(purchaseOrder.getServiceFee(), purchaseOrder.getExchangeRateSnapshot()))"),
        // 权限字段
        @Mapping(target = "canCancel", expression = "java(canCancel(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canPay", expression = "java(canPay(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canConfirmReceipt", expression = "java(canConfirmReceipt(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canApplyRefund", expression = "java(canApplyRefund(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canViewTracking", expression = "java(canViewTracking(purchaseOrder.getOrderStatus()))"),
        // 主要商品信息
        @Mapping(target = "mainProductTitle", expression = "java(getMainProductTitle(orderItems))"),
        @Mapping(target = "mainProductTitleEn", expression = "java(getMainProductTitleEn(orderItems))"),
        @Mapping(target = "mainProductImageUrl", expression = "java(getMainProductImageUrl(orderItems))"),
        // 统计字段
        @Mapping(target = "supplierCount", source = "purchaseOrder.supplierCount"),
        @Mapping(target = "lineItemCount", source = "purchaseOrder.lineItemCount"),
        @Mapping(target = "completedSupplierCount", source = "purchaseOrder.completedSupplierCount"),
        // 其他字段
        @Mapping(target = "buyerMessage", source = "purchaseOrder.purchaseNotes"),
        @Mapping(target = "deliveryTime", expression = "java(getDeliveryTime(purchaseOrder))"),
        @Mapping(target = "completedTime", expression = "java(getCompletedTime(purchaseOrder))"),
        // 订单商品信息
        @Mapping(target = "orderItems", expression = "java(convertOrderItems(orderItems, purchaseOrder.getExchangeRateSnapshot()))")
    })
    UserPurchaseOrderDTO convertToUserPurchaseOrderDTO(TzOrderPurchase purchaseOrder, List<TzOrderItem> orderItems);

    /**
     * 转换DTO为VO
     *
     * @param dto UserPurchaseOrderDTO
     * @return UserPurchaseOrderPageVO
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "purchaseOrderNo"),
        @Mapping(target = "payTime", source = "paidDate"),
        @Mapping(target = "totalAmount", source = "customerTotalAmount"),
        @Mapping(target = "shippingFee", source = "customerTotalFreight")
    })
    UserPurchaseOrderPageVO convertToUserPurchaseOrderPageVO(UserPurchaseOrderDTO dto);

    // ==================== 辅助方法 ====================

    /**
     * 获取订单状态显示文本
     */
    default String getOrderStatusText(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "未知状态";
        }
        return switch (status) {
            case TEMPORARILY_SAVED -> "订单保存中";
            case PAYMENT_PENDING -> "待支付";
            case PAYMENT_COMPLETED -> "支付成功";
            case PROCUREMENT_IN_PROGRESS -> "采购中";
            case PARTIALLY_PROCUREMENT -> "部分履约";
            case SUPPLIER_SHIPPED -> "商品发货中";
            case WAREHOUSE_PENDING_RECEIVED -> "运输中";
            case WAREHOUSE_RECEIVED -> "已到仓库";
            case IN_STOCK -> "已完成";
            case ORDER_CANCELLED -> "已取消";
            default -> status.getDescription();
        };
    }

    /**
     * 获取订单状态描述
     */
    default String getOrderStatusDescription(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "订单状态异常";
        }
        return switch (status) {
            case TEMPORARILY_SAVED -> "订单已保存，等待进一步处理。可能是询价商品需要代付款或余额不足";
            case PAYMENT_PENDING -> "订单已确认，请尽快完成支付。支持余额支付、Stripe支付等多种方式";
            case PAYMENT_COMPLETED -> "支付成功，我们正在为您准备商品。订单已进入内部处理流程";
            case PROCUREMENT_IN_PROGRESS -> "采购员正在处理您的订单，正在向供应商下单采购商品";
            case PARTIALLY_PROCUREMENT -> "部分供应商订单已完成，其他订单仍在处理中。您可以查看各商品的详细进展";
            case SUPPLIER_SHIPPED -> "供应商已将商品发往我们的仓库，商品正在运输途中";
            case WAREHOUSE_PENDING_RECEIVED -> "商品正在运输途中，等待我们的仓库接收";
            case WAREHOUSE_RECEIVED -> "商品已到达仓库，正在进行质检和上架流程";
            case IN_STOCK -> "订单已完成，商品已通过质检并成功入库。感谢您的购买！";
            case ORDER_CANCELLED -> "订单已取消。如有疑问请联系客服";
            default -> "订单状态：" + status.getDescription();
        };
    }

    /**
     * 获取订单状态图标
     */
    default String getOrderStatusIcon(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "question-circle";
        }
        return switch (status) {
            case TEMPORARILY_SAVED -> "save";
            case PAYMENT_PENDING -> "credit-card";
            case PAYMENT_COMPLETED -> "check-circle";
            case PROCUREMENT_IN_PROGRESS -> "shopping-cart";
            case PARTIALLY_PROCUREMENT -> "clock-circle";
            case SUPPLIER_SHIPPED -> "truck";
            case WAREHOUSE_PENDING_RECEIVED -> "loading";
            case WAREHOUSE_RECEIVED -> "home";
            case IN_STOCK -> "trophy";
            case ORDER_CANCELLED -> "close-circle";
            default -> "info-circle";
        };
    }

    /**
     * 获取订单状态颜色
     */
    default String getOrderStatusColor(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "#d9d9d9";
        }
        return switch (status) {
            case TEMPORARILY_SAVED -> "#faad14";
            case PAYMENT_PENDING -> "#ff7a45";
            case PAYMENT_COMPLETED -> "#52c41a";
            case PROCUREMENT_IN_PROGRESS -> "#1890ff";
            case PARTIALLY_PROCUREMENT -> "#722ed1";
            case SUPPLIER_SHIPPED -> "#13c2c2";
            case WAREHOUSE_PENDING_RECEIVED -> "#2f54eb";
            case WAREHOUSE_RECEIVED -> "#389e0d";
            case IN_STOCK -> "#52c41a";
            case ORDER_CANCELLED -> "#ff4d4f";
            default -> "#d9d9d9";
        };
    }

    /**
     * 获取订单状态标签类型
     */
    default String getOrderStatusTagType(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "info";
        }
        return switch (status) {
            case TEMPORARILY_SAVED, PAYMENT_PENDING -> "warning";
            case PAYMENT_COMPLETED -> "success";
            case PROCUREMENT_IN_PROGRESS, WAREHOUSE_PENDING_RECEIVED -> "info";
            case PARTIALLY_PROCUREMENT, SUPPLIER_SHIPPED -> "primary";
            case WAREHOUSE_RECEIVED, IN_STOCK -> "success";
            case ORDER_CANCELLED -> "danger";
            default -> "info";
        };
    }

    /**
     * 获取订单进度百分比
     */
    default Integer getProgressPercentage(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return 0;
        }
        return switch (status) {
            case TEMPORARILY_SAVED -> 5;
            case PAYMENT_PENDING -> 10;
            case PAYMENT_COMPLETED -> 20;
            case PROCUREMENT_IN_PROGRESS -> 40;
            case PARTIALLY_PROCUREMENT -> 60;
            case SUPPLIER_SHIPPED -> 70;
            case WAREHOUSE_PENDING_RECEIVED -> 80;
            case WAREHOUSE_RECEIVED -> 90;
            case IN_STOCK -> 100;
            case ORDER_CANCELLED -> 0;
            default -> 0;
        };
    }

    /**
     * 汇率转换为美元
     */
    default BigDecimal convertToUsd(BigDecimal amount, BigDecimal exchangeRateSnapshot) {
        if (amount == null) {
            return null;
        }

        // 如果有汇率快照且不为0，使用快照汇率
        if (exchangeRateSnapshot != null && exchangeRateSnapshot.compareTo(BigDecimal.ZERO) > 0) {
            return amount.multiply(exchangeRateSnapshot);
        }

        // 否则使用实时汇率转换
        return CurrencyConversionUtils.cnyToUsd(amount);
    }

    /**
     * 判断订单是否可以取消
     */
    default Boolean canCancel(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isCancellable();
    }

    /**
     * 判断订单是否可以支付
     */
    default Boolean canPay(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isPayable();
    }

    /**
     * 判断订单是否可以确认收货
     * 注意：当前状态枚举只到WMS入库，没有发货给客户的状态
     */
    default Boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        // 当前状态枚举只到WMS入库，没有发货给客户的状态
        // 如果后续需要客户确认收货功能，需要扩展状态枚举
        return false;
    }

    /**
     * 判断订单是否可以申请退款
     */
    default Boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 支付完成后到完成前的状态都可以申请退款
        return status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED ||
            status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 判断订单是否可以查看物流
     */
    default Boolean canViewTracking(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 采购中及以后的状态可以查看物流
        return status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 获取主要商品标题
     */
    default String getMainProductTitle(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return null;
        }
        return orderItems.get(0).getProductTitle();
    }

    /**
     * 获取主要商品英文标题
     */
    default String getMainProductTitleEn(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return null;
        }
        return orderItems.get(0).getProductTitleEn();
    }

    /**
     * 获取主要商品图片
     */
    default String getMainProductImageUrl(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return null;
        }
        return orderItems.getFirst().getProductImageUrl();
    }

    /**
     * 获取发货时间
     */
    default LocalDateTime getDeliveryTime(TzOrderPurchase purchaseOrder) {
        // 根据订单状态判断发货时间
        if (purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.IN_STOCK) {
            // 这里可以根据实际业务逻辑返回发货时间
            // 暂时返回更新时间作为发货时间的近似值
            return purchaseOrder.getGmtModified();
        }
        return null;
    }

    /**
     * 获取完成时间
     */
    default LocalDateTime getCompletedTime(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.IN_STOCK) {
            return purchaseOrder.getOrderCompletedDate();
        }
        return null;
    }

    /**
     * 转换订单商品信息
     */
    default List<UserPurchaseOrderDTO.OrderItemInfoDTO> convertOrderItems(List<TzOrderItem> orderItems, BigDecimal exchangeRateSnapshot) {
        if (orderItems == null || orderItems.isEmpty()) {
            return List.of();
        }

        return orderItems.stream()
            .map(item -> convertOrderItem(item, exchangeRateSnapshot))
            .toList();
    }

    /**
     * 转换单个订单商品
     */
    default UserPurchaseOrderDTO.OrderItemInfoDTO convertOrderItem(TzOrderItem orderItem, BigDecimal exchangeRateSnapshot) {
        UserPurchaseOrderDTO.OrderItemInfoDTO dto = new UserPurchaseOrderDTO.OrderItemInfoDTO();
        dto.setId(String.valueOf(orderItem.getId()));
        dto.setPurchaseOrderId(orderItem.getPurchaseOrderId());
        dto.setSupplierOrderId(orderItem.getSupplierOrderId());
        dto.setProductId(orderItem.getProductSpuId());
        dto.setSkuId(orderItem.getProductSkuId());
        dto.setProductImage(orderItem.getProductImageUrl());
        dto.setProductTitle(orderItem.getProductTitle());
        dto.setProductTitleEn(orderItem.getProductTitleEn());
        dto.setSkuSpecs(orderItem.getSkuSpecs());
        dto.setOrderedQuantity(orderItem.getQuantity() != null ? orderItem.getQuantity().intValue() : null);
        dto.setUnitOfMeasure(orderItem.getUnit());
        dto.setUnitPrice(orderItem.getPrice());
        dto.setUnitPriceUsd(convertToUsd(orderItem.getPrice(), exchangeRateSnapshot));
        dto.setLineTotalAmount(orderItem.getTotalAmount());
        dto.setLineTotalAmountUsd(convertToUsd(orderItem.getTotalAmount(), exchangeRateSnapshot));
        return dto;
    }

    /**
     * 获取服务费费率
     */
    default BigDecimal getServiceFeeRate() {
        // 这里可以根据业务逻辑返回服务费费率
        // 暂时返回默认值，实际应该从配置或计算得出
        return new BigDecimal("0.05"); // 5%
    }

    /**
     * 获取币种
     */
    default String getCurrency() {
        // 默认返回人民币
        return "CNY";
    }
}
