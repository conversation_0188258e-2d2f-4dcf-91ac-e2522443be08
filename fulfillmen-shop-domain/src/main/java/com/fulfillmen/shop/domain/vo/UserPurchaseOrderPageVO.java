/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 用户订单列表视图对象
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 用户端订单列表显示的数据
 */
@Data
@Schema(description = "用户订单列表")
public class UserPurchaseOrderPageVO {

    @Schema(description = "采购单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单状态")
    private TzOrderPurchaseStatusEnum orderStatus;

    @Schema(description = "订单状态显示名称")
    private String orderStatusText;

    @Schema(description = "订单状态描述")
    private String orderStatusDescription;

    @Schema(description = "订单状态图标")
    private String orderStatusIcon;

    @Schema(description = "订单状态颜色")
    private String orderStatusColor;

    @Schema(description = "订单状态标签类型")
    private String orderStatusTagType;

    @Schema(description = "订单进度百分比")
    private Integer progressPercentage;

    @Schema(description = "订单总金额")
    private BigDecimal totalAmount;

    @Schema(description = "订单总金额(美元)")
    private BigDecimal totalAmountUsd;

    @Schema(description = "运费")
    private BigDecimal shippingFee;

    @Schema(description = "运费(美元)")
    private BigDecimal shippingFeeUsd;

    @Schema(description = "服务费费率")
    private BigDecimal serviceFeeRate;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "订单商品总数量")
    private Integer totalQuantity;

    @Schema(description = "商品种类数")
    private Integer productTypeCount;

    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "订单更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    @Schema(description = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedTime;

    @Schema(description = "主要商品标题")
    private String mainProductTitle;

    @Schema(description = "主要商品英文标题")
    private String mainProductTitleEn;

    @Schema(description = "主要商品图片")
    private String mainProductImageUrl;

    @Schema(description = "买家留言")
    private String buyerMessage;

    // 操作权限字段
    @Schema(description = "是否可以取消")
    private Boolean canCancel;

    @Schema(description = "是否可以支付")
    private Boolean canPay;

    @Schema(description = "是否可以确认收货")
    private Boolean canConfirmReceipt;

    @Schema(description = "是否可以申请退款")
    private Boolean canApplyRefund;

    @Schema(description = "是否可以查看物流")
    private Boolean canViewTracking;

    @Schema(description = "订单商品信息（预览用）")
    private List<OrderItemInfo> orderItems;

    /**
     * 订单商品信息
     */
    @Data
    @Schema(description = "订单商品信息")
    public static class OrderItemInfo {

        @Schema(description = "订单项ID")
        private String id;

        @Schema(description = "采购订单ID")
        private Long purchaseOrderId;

        @Schema(description = "供应商订单ID")
        private Long supplierOrderId;

        @Schema(description = "商品ID")
        private Long productId;

        @Schema(description = "SKU ID")
        private Long skuId;

        @Schema(description = "商品图片")
        private String productImage;

        @Schema(description = "商品标题")
        private String productTitle;

        @Schema(description = "商品标题(英文)")
        private String productTitleEn;

        @Schema(description = "商品规格")
        private List<AttrJson> skuSpecs;

        @Schema(description = "商品数量")
        private Integer orderedQuantity;

        @Schema(description = "计量单位")
        private String unitOfMeasure;

        @Schema(description = "商品单价")
        private BigDecimal unitPrice;

        @Schema(description = "商品单价(美元)")
        private BigDecimal unitPriceUsd;

        @Schema(description = "行总金额")
        private BigDecimal lineTotalAmount;

        @Schema(description = "行总金额(美元)")
        private BigDecimal lineTotalAmountUsd;

        @Schema(description = "商品状态")
        private String itemStatus;

        @Schema(description = "商品状态描述")
        private String itemStatusName;

        @Schema(description = "商品是否可用")
        private Boolean available;

        @Schema(description = "商品消息")
        private String message;
    }
}
