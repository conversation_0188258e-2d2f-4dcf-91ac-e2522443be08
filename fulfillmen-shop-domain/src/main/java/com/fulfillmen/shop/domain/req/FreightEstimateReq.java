/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.req;

import com.fulfillmen.support.alibaba.api.request.logistics.ProductFreightEstimateRequestRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/13
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FreightEstimateReq {

    @Schema(description = "1688商品ID")
    @NotNull(message = "offerId不能为空")
    private Long offerId;

    @Schema(description = "目标省份代码")
    @NotBlank(message = "toProvinceCode不能为空")
    private String toProvinceCode;

    @Schema(description = "目标城市代码")
    @NotBlank(message = "toCityCode不能为空")
    private String toCityCode;

    @Schema(description = "目标国家代码")
    @NotBlank(message = "toCountryCode不能为空")
    private String toCountryCode;

    @NotNull(message = "totalNum不能为空")
    @Schema(description = "总购买数量")
    @Min(value = 1, message = "商品总数量必须大于0")
    private Long totalNum;

    @Schema(description = "各sku购买数量列表")
    @NotNull(message = "logisticsSkuNumModels不能为空")
    private List<ProductFreightEstimateRequestRecord.LogisticsSkuNumModelRecord> logisticsSkuNumModels;
}
