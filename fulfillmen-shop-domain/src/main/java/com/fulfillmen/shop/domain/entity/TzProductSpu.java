/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品信息
 *
 * <AUTHOR>
 * @date 2025/5/9 17:26
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TzProductSpu implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 产品标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 产品 标题翻译
     */
    @TableField(value = "title_trans")
    private String titleTrans;

    /**
     * 产品描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 产品描述翻译
     */
    @TableField(value = "description_trans")
    private String descriptionTrans;

    /**
     * 产品名称 (iPhone 15)
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 产品名称翻译
     */
    @TableField(value = "name_trans")
    private String nameTrans;

    /**
     * 类目 id
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 类目名称
     */
    @TableField(value = "category_name")
    private String categoryName;

    /**
     * 类目名称翻译
     */
    @TableField(value = "category_name_trans")
    private String categoryNameTrans;

    /**
     * 主图URL
     */
    @TableField(value = "main_image")
    private String mainImage;

    /**
     * 白底图
     */
    @TableField(value = "white_image")
    private String whiteImage;

    /**
     * 产品图片列表,json 数组
     */
    @TableField(value = "images")
    private String images;

    /**
     * 单位 中文
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 单位 英文
     */
    @TableField(value = "unit_trans")
    private String unitTrans;

    /**
     * 产品主视频
     */
    @TableField(value = "main_video")
    private String mainVideo;

    /**
     * 产品详情视频
     */
    @TableField(value = "detail_video")
    private String detailVideo;

    /**
     * 商品(Category-Property-Value)CPV 属性，[{}] 。对应商品的属性列表
     */
    @TableField(value = "attribute_cpvs")
    private String attributeCpvs;

    /**
     * 商品包裹配送信息相关数据，数据结构不同平台可能不同。{weightwidthheightlength}
     */
    @TableField(value = "shipping_info")
    private String shippingInfo;

    /**
     * 商品证书列表
     */
    @TableField(value = "certificate_list")
    private String certificateList;

    /**
     * sku件重尺集合。1688 shipping_info字段下，我将它提炼出来。[]
     */
    @TableField(value = "sku_shipping_details")
    private String skuShippingDetails;

    /**
     * 最低购买数量
     */
    @TableField(value = "min_order_quantity")
    private Integer minOrderQuantity;

    /**
     * 是否来自 pdc_product_mapping 同步过来的产品。=0 否，=1 是
     */
    @TableField(value = "is_pdc_sync")
    private PdcProductMappingSyncStatusEnum isPdcSync;

    /**
     * 是否单品 =0 否、1 是，无 SKU 的产品信息。来源如果不存在 sku 将默认创建一个虚拟的 sku
     */
    @TableField(value = "is_single_item")
    private TzProductSpuSingleItemEnum isSingleItem;

    /**
     * pdc_product_mapping.id ,is_pdc_sync=1 ，此值不能为 null
     */
    @TableField(value = "pdc_product_mapping_id")
    private Long pdcProductMappingId;

    /**
     * 平台
     */
    @TableField(value = "platform_code")
    private PlatformCodeEnum platformCode;

    /**
     * 平台默认的产品 id ，via: 1688 offerId
     */
    @TableField(value = "pdc_platform_product_id")
    private String pdcPlatformProductId;

    /**
     * 平台默认的产品 id ，via: 1688 卖家 ID
     */
    @TableField(value = "source_platform_seller_open_id")
    private String sourcePlatformSellerOpenId;

    /**
     * 平台默认的产品 id ，via: 1688 卖家名称
     */
    @TableField(value = "source_platform_seller_name")
    private String sourcePlatformSellerName;

    /**
     * 状态: 0 on_shelf(上架)/1 off_shelf(下架)
     */
    @TableField(value = "`status`")
    private TzProductStatusEnum status;

    /**
     * 上架时间
     */
    @TableField(value = "putaway_time")
    private LocalDateTime putawayTime;

    /**
     * 所属租户
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除；0 否，>0 是，默认 0 如果删除，默认将 ID 值赋给 is_deleted
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    private Integer revision;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
