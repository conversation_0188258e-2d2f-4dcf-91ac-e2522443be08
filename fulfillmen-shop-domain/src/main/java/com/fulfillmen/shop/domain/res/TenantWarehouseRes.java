/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.res;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 租户仓库信息响应
 *
 * <AUTHOR>
 * @date 2025/8/12
 * @description: todo
 * @since 1.0.0
 */
@Data
@Schema(description = "仓库列表响应")
public class TenantWarehouseRes {

    @Schema(description = "仓库ID")
    private Long id;

    @Schema(description = "仓库名称")
    private String name;

    @Schema(description = "仓库名称英文")
    private String nameEn;

    @Schema(description = "仓库编码")
    private String warehouseCode;

    @Schema(description = "仓库描述")
    private String warehouseDesc;

    @Schema(description = "仓库图标")
    private String warehouseIcon;

    @Schema(description = "仓库颜色")
    private String warehouseColor;

    @Schema(description = "仓库类型 1-国内仓, 2-海外仓")
    private String warehouseType;

    @Schema(description = "仓库排序")
    private Integer warehouseSort;

    @Schema(description = "是否默认仓库 0-否, 1-是")
    private Integer isDefault;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区县")
    private String district;

    @Schema(description = "区县编码")
    private String districtCode;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "邮编")
    private String postcode;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Schema(description = "联系人手机")
    private String contactMobile;
}
