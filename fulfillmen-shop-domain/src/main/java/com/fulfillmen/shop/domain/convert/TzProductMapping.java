/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSellerDataInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSellerDataDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuSpecDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.domain.entity.json.TzProductSkuSalesInfo;
import com.fulfillmen.shop.domain.entity.json.TzProductSkuUnitInfo;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * TzProduct 映射转换器
 *
 * <AUTHOR>
 * @date 2025/6/16 10:00
 * @description: 使用 Mapstruct 实现 AlibabaProductDetailDTO 到
 *               TzProductSpu/TzProductSku 的映射转换
 * @since 1.0.0
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TzProductMapping {

    TzProductMapping INSTANCE = Mappers.getMapper(TzProductMapping.class);

    // ==================== 常量定义 ====================
    String SKU_PREFIX = "SKU_";
    String DEFAULT_CATEGORY_NAME = "默认分类";
    String DEFAULT_CATEGORY_NAME_EN = "Default Category";
    String ID_GENERATOR_NAME = "safe-js";

    // ==================== SPU 映射 ====================

    /**
     * 将 AlibabaProductDetailDTO 转换为 TzProductSpu
     *
     * @param productDetail 阿里巴巴产品详情DTO
     * @return TzProductSpu 实体
     */
    @Mapping(target = "id", expression = "java(generateId())")
    @Mapping(target = "platformCode", constant = "PLATFORM_CODE_1688")
    @Mapping(target = "pdcPlatformProductId", source = "productDetail.platformProductId")
    @Mapping(target = "title", source = "productDetail.title")
    @Mapping(target = "titleTrans", source = "productDetail.titleTrans")
    @Mapping(target = "description", source = "productDetail.description")
    @Mapping(target = "descriptionTrans", ignore = true) // AlibabaProductDetailDTO 没有此字段
    @Mapping(target = "name", source = "productDetail.title") // 使用标题作为产品名称
    @Mapping(target = "nameTrans", source = "productDetail.titleTrans") // 使用标题翻译作为产品名称翻译
    @Mapping(target = "categoryId", expression = "java(safeGetCategoryId(productDetail))")
    @Mapping(target = "categoryName", expression = "java(safeGetCategoryName(productDetail))")
    @Mapping(target = "categoryNameTrans", expression = "java(safeGetCategoryNameTrans(productDetail))")
    @Mapping(target = "mainImage", expression = "java(extractMainImageFromProductDetail(productDetail))")
    @Mapping(target = "whiteImage", source = "productDetail.whiteImage")
    @Mapping(target = "images", expression = "java(extractImagesJson(productDetail))")
    @Mapping(target = "unit", source = "productDetail.unit")
    @Mapping(target = "unitTrans", source = "productDetail.unitTrans")
    @Mapping(target = "mainVideo", source = "productDetail.mainVideo")
    @Mapping(target = "detailVideo", source = "productDetail.detailVideo")
    @Mapping(target = "attributeCpvs", expression = "java(extractAttributeCpvsJson(productDetail))")
    @Mapping(target = "shippingInfo", expression = "java(extractShippingInfoJson(productDetail))")
    @Mapping(target = "certificateList", expression = "java(extractCertificateListJson(productDetail))")
    @Mapping(target = "skuShippingDetails", expression = "java(extractSkuShippingDetailsJson(productDetail))")
    @Mapping(target = "minOrderQuantity", expression = "java(extractMinOrderQuantity(productDetail))")
    @Mapping(target = "isPdcSync", constant = "SYNCED") // 标记为从PDC同步
    @Mapping(target = "pdcProductMappingId", source = "productDetail.id")
    @Mapping(target = "status", constant = "ON_SHELF") // 默认上架
    @Mapping(target = "putawayTime", expression = "java(java.time.LocalDateTime.now())") // 上架时间
    @Mapping(target = "isSingleItem", expression = "java(mapSingleItemEnum(productDetail.isSingleItem()))")
    @Mapping(target = "sourcePlatformSellerOpenId", source = "productDetail.sellerOpenId")
    @Mapping(target = "sourcePlatformSellerName", source = "productDetail.sellerName")
    TzProductSpu toTzProductSpu(AlibabaProductDetailDTO productDetail);

    // ==================== SKU 映射 ====================

    /**
     * 将 AlibabaProductSkuDTO 转换为 TzProductSku
     *
     * @param skuDTO            阿里巴巴SKU DTO
     * @param spuId             关联的SPU ID
     * @param platformProductId 平台产品ID
     * @return TzProductSku 实体
     */
    @Mapping(target = "id", expression = "java(generateId())")
    @Mapping(target = "spuId", source = "spuId")
    @Mapping(target = "platformCode", constant = "PLATFORM_CODE_1688")
    @Mapping(target = "platformProductId", source = "platformProductId")
    @Mapping(target = "platformSpecId", expression = "java(skuDTO.getSpecId())")
    @Mapping(target = "platformSku", expression = "java(String.valueOf(skuDTO.getSkuId()))")
    @Mapping(target = "sku", expression = "java(buildSkuCode(skuDTO.getSkuId()))")
    @Mapping(target = "barcode", expression = "java(skuDTO.getSkuId().toString())")
    @Mapping(target = "image", expression = "java(extractMainImageFromSpecs(skuDTO))")
    @Mapping(target = "quantity", expression = "java(safeGetQuantity(skuDTO.getAmountOnSale()))")
    @Mapping(target = "price", expression = "java(safeGetPrice(skuDTO.getPrice()))")
    @Mapping(target = "dropShippingPrice", expression = "java(getDropShippingPrice(skuDTO))")
    @Mapping(target = "salesInfo", expression = "java(extractSalesInfoFromSkuDTO(skuDTO))")
    @Mapping(target = "specs", expression = "java(buildSpecsFromSkuDTO(skuDTO))")
    @Mapping(target = "salesCount", constant = "0")
    @Mapping(target = "unitInfo", expression = "java(extractUnitInfoFromSkuDTO(skuDTO))")
    TzProductSku toTzProductSku(AlibabaProductSkuDTO skuDTO, Long spuId, String platformProductId);

    @Mapping(target = "id", expression = "java(generateId())")
    @Mapping(target = "spuId", source = "spuId")
    @Mapping(target = "platformCode", constant = "PLATFORM_CODE_1688")
    @Mapping(target = "platformProductId", source = "platformProductId")
    @Mapping(target = "platformSpecId", expression = "java(skuDTO.getSpecId())")
    @Mapping(target = "platformSku", expression = "java(String.valueOf(skuDTO.getSkuId()))")
    @Mapping(target = "sku", expression = "java(buildSkuCode(skuDTO.getSkuId()))")
    @Mapping(target = "barcode", expression = "java(skuDTO.getSkuId().toString())")
    @Mapping(target = "image", expression = "java(extractMainImageFromSpecs(skuDTO))")
    @Mapping(target = "quantity", expression = "java(safeGetQuantity(skuDTO.getAmountOnSale()))")
    @Mapping(target = "minOrderQuantity", expression = "java(minOrderQuantity==null?1:minOrderQuantity)")
    @Mapping(target = "price", expression = "java(safeGetPrice(skuDTO.getPrice()))")
    @Mapping(target = "dropShippingPrice", expression = "java(getDropShippingPrice(skuDTO))")
    @Mapping(target = "salesInfo", expression = "java(extractSalesInfoFromSkuDTO(skuDTO))")
    @Mapping(target = "specs", expression = "java(buildSpecsFromSkuDTO(skuDTO))")
    @Mapping(target = "salesCount", constant = "0")
    @Mapping(target = "unitInfo", expression = "java(extractUnitInfoFromSkuDTO(skuDTO))")
    TzProductSku toTzProductSku(AlibabaProductSkuDTO skuDTO, Long spuId, String platformProductId, Integer minOrderQuantity);

    /**
     * 为单品创建默认SKU
     *
     * @param productDetail 阿里巴巴产品详情DTO
     * @param spuId         关联的SPU ID
     * @return TzProductSku 实体
     */
    @Mapping(target = "id", expression = "java(generateId())")
    @Mapping(target = "spuId", source = "spuId")
    @Mapping(target = "platformCode", constant = "PLATFORM_CODE_1688")
    @Mapping(target = "platformProductId", source = "productDetail.platformProductId")
    @Mapping(target = "platformSpecId", source = "productDetail.platformProductId") // 对于单品，SKU ID 等于产品ID
    @Mapping(target = "platformSku", source = "productDetail.platformProductId") // 对于单品，SKU ID 等于产品ID
    @Mapping(target = "sku", expression = "java(buildSkuCodeForSingleItem())")
    @Mapping(target = "barcode", expression = "java(String.valueOf(generateId()))")
    @Mapping(target = "image", expression = "java(extractSkuImageFromProductDetail(productDetail))")
    @Mapping(target = "quantity", expression = "java(extractQuantityFromProductDetail(productDetail))")
    @Mapping(target = "price", expression = "java(extractPriceFromProductDetail(productDetail))")
    @Mapping(target = "dropShippingPrice", expression = "java(extractDropShippingPriceFromProductDetail(productDetail))")
    @Mapping(target = "salesInfo", expression = "java(extractSalesInfoFromProductDetail(productDetail))")
    @Mapping(target = "specs", expression = "java(createEmptySpecs())")
    @Mapping(target = "unitInfo", expression = "java(extractUnitInfoFromProductDetail(productDetail))")
    TzProductSku toSingleItemSku(AlibabaProductDetailDTO productDetail, Long spuId);

    /**
     * 批量转换SKU列表
     *
     * @param skuDTOList        SKU DTO列表
     * @param spuId             关联的SPU ID
     * @param platformProductId 平台产品ID
     * @return TzProductSku 列表
     */
    default List<TzProductSku> toTzProductSkuList(List<AlibabaProductSkuDTO> skuDTOList,
        Long spuId,
        String platformProductId, Integer minOrderQuantity) {
        if (CollectionUtil.isEmpty(skuDTOList)) {
            return new ArrayList<>();
        }

        List<TzProductSku> result = new ArrayList<>();
        for (AlibabaProductSkuDTO skuDTO : skuDTOList) {
            TzProductSku sku = toTzProductSku(skuDTO, spuId, platformProductId, minOrderQuantity);
            if (sku != null) {
                result.add(sku);
            }
        }
        return result;
    }

    // ==================== 工具方法 ====================

    /**
     * 将 TzProductSpu 和 SKU 列表转换为 TzProductDTO
     *
     * @param spu                     产品 SPU
     * @param skuList                 SKU 列表
     * @param alibabaProductDetailDTO
     * @return TzProductDTO
     */
    default TzProductDTO toTzProductDTO(TzProductSpu spu,
        List<TzProductSku> skuList,
        AlibabaProductDetailDTO alibabaProductDetailDTO) {
        if (spu == null) {
            return null;
        }

        var productDTO = TzProductDTO.builder()
            .id(spu.getId())
            .title(spu.getTitle())
            .titleTrans(spu.getTitleTrans())
            .description(spu.getDescription())
            .descriptionTrans(spu.getDescriptionTrans())
            .name(spu.getName())
            .nameTrans(spu.getNameTrans())
            .categoryId(spu.getCategoryId())
            .categoryName(spu.getCategoryName())
            .categoryNameTrans(spu.getCategoryNameTrans())
            .mainImage(spu.getMainImage())
            .whiteImage(spu.getWhiteImage())
            .images(spu.getImages())
            .unit(spu.getUnit())
            .unitTrans(spu.getUnitTrans())
            .mainVideo(spu.getMainVideo())
            .detailVideo(spu.getDetailVideo())
            .attributeCpvs(spu.getAttributeCpvs())
            .shippingInfo(spu.getShippingInfo())
            .certificateList(spu.getCertificateList())
            .skuShippingDetails(spu.getSkuShippingDetails())
            .minOrderQuantity(spu.getMinOrderQuantity())
            .isPdcSync(spu.getIsPdcSync())
            .isSingleItem(spu.getIsSingleItem())
            .pdcProductMappingId(spu.getPdcProductMappingId())
            .platformCode(spu.getPlatformCode())
            .pdcPlatformProductId(spu.getPdcPlatformProductId())
            .sourcePlatformSellerOpenId(spu.getSourcePlatformSellerOpenId())
            .sourcePlatformSellerName(spu.getSourcePlatformSellerName())
            .status(spu.getStatus())
            .putawayTime(spu.getPutawayTime())
            .skuList(toTzProductSkuDTOList(skuList))
            .build();

        if (alibabaProductDetailDTO != null) {
            if (alibabaProductDetailDTO.getSellerDataInfo() != null) {
                AlibabaProductSellerDataDTO sellerDataInfo = alibabaProductDetailDTO.getSellerDataInfo();
                productDTO.setSellerDataInfo(TzProductSellerDataInfoDTO.builder()
                    .tradeMedalLevel(String.valueOf(sellerDataInfo.getTradeMedalLevel()))
                    .compositeServiceScore(sellerDataInfo.getCompositeServiceScore())
                    .logisticsExperienceScore(sellerDataInfo.getLogisticsExperienceScore())
                    .disputeComplaintScore(sellerDataInfo.getDisputeComplaintScore())
                    .offerExperienceScore(sellerDataInfo.getOfferExperienceScore())
                    .afterSalesExperienceScore(sellerDataInfo.getAfterSalesExperienceScore())
                    .consultingExperienceScore(sellerDataInfo.getConsultingExperienceScore())
                    .repeatPurchasePercent(sellerDataInfo.getRepeatPurchasePercent())
                    .build());
            }
            if (alibabaProductDetailDTO.getShippingInfo() != null) {
                productDTO.setShippingInfo(JSONUtil.toJsonStr(alibabaProductDetailDTO.getShippingInfo()));
            }
        }

        return productDTO;
    }

    /**
     * 将 TzProductSku 转换为 TzProductDTO.TzProductSkuDTO
     *
     * @param sku SKU 实体
     * @return TzProductDTO.TzProductSkuDTO
     */
    default TzProductSkuDTO toTzProductSkuDTO(TzProductSku sku) {
        if (sku == null) {
            return null;
        }

        return TzProductSkuDTO.builder()
            .id(sku.getId())
            .spuId(sku.getSpuId())
            .platformCode(sku.getPlatformCode())
            .platformProductId(sku.getPlatformProductId())
            .platformSku(sku.getPlatformSku())
            .platformSpecId(sku.getPlatformSpecId())
            .sku(sku.getSku())
            .barcode(sku.getBarcode())
            .image(sku.getImage())
            .quantity(sku.getQuantity())
            .minOrderQuantity(sku.getMinOrderQuantity())
            .price(sku.getPrice())
            .usdPrice(calculateUsdPrice(sku.getPrice()))
            .dropShippingPrice(sku.getDropShippingPrice())
            .usdDropShippingPrice(calculateUsdPrice(sku.getDropShippingPrice()))
            .salesInfo(sku.getSalesInfo())
            .specs(sku.getSpecs())
            .salesCount(sku.getSalesCount())
            .unitInfo(sku.getUnitInfo())
            .build();
    }

    /**
     * 批量转换 SKU 列表为 DTO 列表
     *
     * @param skuList SKU 实体列表
     * @return TzProductDTO.TzProductSkuDTO 列表
     */
    default List<TzProductSkuDTO> toTzProductSkuDTOList(List<TzProductSku> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return new ArrayList<>();
        }

        List<TzProductSkuDTO> result = new ArrayList<>();
        for (TzProductSku sku : skuList) {
            TzProductSkuDTO skuDTO = toTzProductSkuDTO(sku);
            if (skuDTO != null) {
                result.add(skuDTO);
            }
        }
        return result;
    }

    /**
     * 生成唯一ID
     */
    default Long generateId() {
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired(ID_GENERATOR_NAME);
        return idGenerator.generate();
    }

    /**
     * 安全获取价格，避免null值
     */
    default BigDecimal safeGetPrice(BigDecimal price) {
        return price != null ? price : BigDecimal.ZERO;
    }

    /**
     * 安全获取数量，避免null值
     */
    default Integer safeGetQuantity(Integer quantity) {
        return quantity != null ? quantity : 0;
    }

    /**
     * 映射单品枚举
     */
    default TzProductSpuSingleItemEnum mapSingleItemEnum(boolean isSingleItem) {
        return TzProductSpuSingleItemEnum.getByValue(isSingleItem ? 1 : 0);
    }

    /**
     * 安全获取分类ID
     */
    default Long safeGetCategoryId(AlibabaProductDetailDTO productDetail) {
        return productDetail.getCategoryId() != null ? productDetail.getCategoryId() : 0L;
    }

    /**
     * 安全获取分类名称
     */
    default String safeGetCategoryName(AlibabaProductDetailDTO productDetail) {
        return productDetail.getCategoryName() != null ? productDetail.getCategoryName() : DEFAULT_CATEGORY_NAME;
    }

    /**
     * 安全获取分类名称翻译
     */
    default String safeGetCategoryNameTrans(AlibabaProductDetailDTO productDetail) {
        return productDetail.getCategoryNameTrans() != null
            ? productDetail.getCategoryNameTrans()
            : DEFAULT_CATEGORY_NAME_EN;
    }

    /**
     * 从产品详情中提取主图
     */
    default String extractMainImageFromProductDetail(AlibabaProductDetailDTO productDetail) {
        if (CollectionUtil.isNotEmpty(productDetail.getImages())) {
            return productDetail.getImages().get(0);
        } else if (StringUtils.hasText(productDetail.getWhiteImage())) {
            return productDetail.getWhiteImage();
        }
        return null;
    }

    /**
     * 从产品详情中提取图片JSON
     */
    default String extractImagesJson(AlibabaProductDetailDTO productDetail) {
        return productDetail.getImages() != null ? JSONUtil.toJsonStr(productDetail.getImages()) : null;
    }

    /**
     * 构建SKU编码
     */
    default String buildSkuCode(Long skuId) {
        return SKU_PREFIX + skuId;
    }

    /**
     * 为单品构建SKU编码
     */
    default String buildSkuCodeForSingleItem() {
        return SKU_PREFIX + generateId();
    }

    /**
     * 获取一件代发价格
     */
    default BigDecimal getDropShippingPrice(AlibabaProductSkuDTO skuDTO) {
        return skuDTO.getOfferPrice() != null ? skuDTO.getOfferPrice() : safeGetPrice(skuDTO.getPrice());
    }

    /**
     * 从SKU DTO构建规格属性
     */
    default List<AttrJson> buildSpecsFromSkuDTO(AlibabaProductSkuDTO skuDTO) {
        List<AttrJson> specs = new ArrayList<>();
        for (AlibabaProductSkuSpecDTO spec : skuDTO.getSpecs()) {
            AttrJson attr = AttrJson.builder()
                // 默认中文
                .attrId(spec.getAttributeId())
                .attrKey(spec.getAttributeName())
                .attrValue(spec.getValue())
                // 英文
                .attrKeyTrans(spec.getAttributeNameTrans())
                .attrValueTrans(spec.getValueTrans())
                .skuImage(spec.getSkuImage())
                .build();
            specs.add(attr);
        }
        return specs;
    }

    /**
     * 从规格中提取主图
     */
    default String extractMainImageFromSpecs(AlibabaProductSkuDTO skuDTO) {
        if (CollectionUtil.isEmpty(skuDTO.getSpecs())) {
            return null;
        }

        return skuDTO.getSpecs()
            .stream()
            .filter(spec -> StringUtils.hasText(spec.getSkuImage()))
            .findFirst()
            .map(AlibabaProductSkuSpecDTO::getSkuImage)
            .orElse(null);
    }

    /**
     * 创建空的规格属性（用于单品）
     */
    default List<AttrJson> createEmptySpecs() {
        return Lists.newArrayList(AttrJson.builder()
            // 中文
            .attrKey("规格")
            .attrValue("默认")
            // 英文
            .attrKeyTrans("specification")
            .attrValueTrans("default")
            .build());
    }

    /**
     * 从产品详情中提取SKU图片
     */
    default String extractSkuImageFromProductDetail(AlibabaProductDetailDTO productDetail) {
        // 优先使用白底图
        if (StringUtils.hasText(productDetail.getWhiteImage())) {
            return productDetail.getWhiteImage();
        }
        // 其次使用第一张图片
        if (CollectionUtil.isNotEmpty(productDetail.getImages())) {
            return productDetail.getImages().get(0);
        }
        return null;
    }

    /**
     * 从产品详情中提取库存数量
     */
    default Integer extractQuantityFromProductDetail(AlibabaProductDetailDTO productDetail) {
        Integer quantity = null;
        if (productDetail.getProductSaleInfo() != null) {
            quantity = productDetail.getProductSaleInfo().getAmountOnSale();
        }
        return safeGetQuantity(quantity);
    }

    /**
     * 从产品详情中提取价格
     */
    default BigDecimal extractPriceFromProductDetail(AlibabaProductDetailDTO productDetail) {
        BigDecimal price = null;

        // 1. 优先从分销销售信息中获取价格
        if (productDetail.getProductSaleInfo() != null && productDetail.getProductSaleInfo()
            .getFenxiaoSaleInfo() != null) {
            price = productDetail.getProductSaleInfo().getFenxiaoSaleInfo().getOfferPrice();
        }

        // 2. 如果没有分销价格，使用产品基础价格
        if (price == null && productDetail.getPrice() != null) {
            price = productDetail.getPrice();
        }

        // 3. 最后从价格区间中获取价格
        if (price == null && productDetail.getProductSaleInfo() != null && CollectionUtil.isNotEmpty(productDetail
            .getProductSaleInfo()
            .getPriceRangeList())) {
            price = productDetail.getProductSaleInfo().getPriceRangeList().get(0).getPrice();
        }

        return safeGetPrice(price);
    }

    /**
     * 从产品详情中提取一件代发价格
     */
    default BigDecimal extractDropShippingPriceFromProductDetail(AlibabaProductDetailDTO productDetail) {
        BigDecimal dropShippingPrice = null;

        // 1. 优先从分销销售信息中获取一件代发价格
        if (productDetail.getProductSaleInfo() != null && productDetail.getProductSaleInfo()
            .getFenxiaoSaleInfo() != null) {
            dropShippingPrice = productDetail.getProductSaleInfo().getFenxiaoSaleInfo().getOnePiecePrice();
        }

        // 2. 如果没有一件代发价格，使用普通价格
        if (dropShippingPrice == null) {
            dropShippingPrice = extractPriceFromProductDetail(productDetail);
        }

        return safeGetPrice(dropShippingPrice);
    }

    /**
     * 从产品详情中提取商品属性JSON
     */
    default String extractAttributeCpvsJson(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getProductAttributeList() != null) {
            return JSONUtil.toJsonStr(productDetail.getProductAttributeList());
        }
        return null;
    }

    /**
     * 从产品详情中提取物流信息JSON
     */
    default String extractShippingInfoJson(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getShippingInfo() != null) {
            return JSONUtil.toJsonStr(productDetail.getShippingInfo());
        }
        return null;
    }

    /**
     * 提取证书信息JSON字符串
     *
     * @param productDetail 阿里巴巴产品详情DTO
     * @return 证书信息JSON字符串
     */
    default String extractCertificateListJson(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getCertificateList() != null && !CollectionUtil.isEmpty(productDetail.getCertificateList())) {
            return JSONUtil.toJsonStr(productDetail.getCertificateList());
        }
        return null;
    }

    /**
     * 从产品详情中提取SKU物流详情JSON
     */
    default String extractSkuShippingDetailsJson(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getShippingInfo() != null && productDetail.getShippingInfo()
            .getSkuShippingDetails() != null) {
            return JSONUtil.toJsonStr(productDetail.getShippingInfo().getSkuShippingDetails());
        }
        return null;
    }

    /**
     * 从产品详情中提取最低购买数量
     */
    default Integer extractMinOrderQuantity(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getProductSaleInfo() != null) {
            return productDetail.getProductSaleInfo().getMinOrderQuantity();
        }
        return 1; // 默认最低购买数量为1
    }

    /**
     * 从SKU DTO中提取销售信息
     */
    default List<TzProductSkuSalesInfo> extractSalesInfoFromSkuDTO(AlibabaProductSkuDTO skuDTO) {
        // 这里可以根据需要从 skuDTO 中提取分销价格规则
        // 目前返回空列表，后续可以扩展
        return new ArrayList<>();
    }

    /**
     * 从SKU DTO中提取单位信息
     */
    default TzProductSkuUnitInfo extractUnitInfoFromSkuDTO(AlibabaProductSkuDTO skuDTO) {
        // 目前返回null，后续可以根据需要扩展
        return null;
    }

    /**
     * 从产品详情中提取销售信息（用于单品）
     */
    default List<TzProductSkuSalesInfo> extractSalesInfoFromProductDetail(AlibabaProductDetailDTO productDetail) {
        List<TzProductSkuSalesInfo> salesInfoList = new ArrayList<>();

        if (productDetail.getProductSaleInfo() != null && CollectionUtil.isNotEmpty(productDetail.getProductSaleInfo()
            .getPriceRangeList())) {

            productDetail.getProductSaleInfo().getPriceRangeList().forEach(priceRange -> {
                TzProductSkuSalesInfo salesInfo = new TzProductSkuSalesInfo();
                salesInfo.setStartQuantity(priceRange.getStartQuantity());
                salesInfo.setPrice(priceRange.getPrice());
                salesInfoList.add(salesInfo);
            });
        }

        return salesInfoList;
    }

    /**
     * 从产品详情中提取单位信息（用于单品）
     */
    default TzProductSkuUnitInfo extractUnitInfoFromProductDetail(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getProductSaleInfo() != null && productDetail.getProductSaleInfo().getUnitInfo() != null) {
            com.fulfillmen.shop.domain.entity.json.TzProductSkuUnitInfo unitInfo = new com.fulfillmen.shop.domain.entity.json.TzProductSkuUnitInfo();
            unitInfo.setUnit(productDetail.getProductSaleInfo().getUnitInfo().getUnit());
            unitInfo.setUnitTrans(productDetail.getProductSaleInfo().getUnitInfo().getUnitTrans());
            return unitInfo;
        }
        return null;
    }

    /**
     * 计算美元价格
     *
     * @param cnyPrice CNY价格
     * @return USD价格，如果转换失败则返回null
     */
    default BigDecimal calculateUsdPrice(BigDecimal cnyPrice) {
        return CurrencyConversionUtils.cnyToUsd(cnyPrice);
    }
}
