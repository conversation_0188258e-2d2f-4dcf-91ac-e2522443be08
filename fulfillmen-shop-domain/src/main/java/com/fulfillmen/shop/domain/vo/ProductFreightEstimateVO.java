/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/13
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductFreightEstimateVO {

    @Schema(description = "商品ID")
    private Long offerId;

    @Schema(description = "运费")
    private String freight;

    @Schema(description = "模板ID")
    private Long templateId;

    @Schema(description = "单个产品重量，单位：kg")
    private Double singleProductWeight;

    @Schema(description = "模板类型")
    private Integer templateType;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "子模板类型")
    private Integer subTemplateType;

    @Schema(description = "子模板名称")
    private String subTemplateName;

    @Schema(description = "首重/件费用")
    private String firstFee;

    @Schema(description = "首重/件单位")
    private String firstUnit;

    @Schema(description = "续重/件费用")
    private String nextFee;

    @Schema(description = "续重/件单位")
    private String nextUnit;

    @Schema(description = "折扣")
    private String discount;

    @Schema(description = "计费类型 0: 按重量，1: 按件数 2: 按体积")
    private String chargeType;

    @Schema(description = "是否包邮")
    private Boolean freePostage;

    @Schema(description = "尺寸值类型 0: 长宽高，1: 长宽，2: 长")
    private Integer sizeValueType;

}
